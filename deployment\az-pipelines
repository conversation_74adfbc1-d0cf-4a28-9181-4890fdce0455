trigger:
  branches:
    include:
      - $(Build.SourceBranchName)  # Dynamically takes the branch name

pool:
  name: 'scaleset-agent'

stages:
  - stage: Build_And_Push_TerritoryOptimization
    displayName: "Build and Push Territory Optimization Docker Image"
    jobs:
      - job: BuildAndPushTerritoryOptimization
        displayName: "Build & Push Territory Optimization Docker Image"
        steps:
          - task: Docker@2
            inputs:
              containerRegistry: 'FAi Container Registry Service Connector'
              repository: 'territory-optimization-$(environment)'
              command: 'buildAndPush'
              Dockerfile: '**/TerritoryOptimization/Dockerfile'
              buildContext: $(Build.SourcesDirectory)
              tags: '$(Build.BuildNumber)'
  - stage: Build_And_Push_TerritoryUpdation
    displayName: "Build and Push Territory Updation Docker Image"
    jobs:
      - job: BuildAndPushTerritoryUpdation
        displayName: "Build & Push Territory Updation Docker Image"
        steps:
          - task: Docker@2
            inputs:
              containerRegistry: 'FAi Container Registry Service Connector'
              repository: 'territory-updation-$(environment)'
              command: 'buildAndPush'
              Dockerfile: '**/TerritoryUpdation/Dockerfile'
              buildContext: $(Build.SourcesDirectory)
              tags: '$(Build.BuildNumber)'
              