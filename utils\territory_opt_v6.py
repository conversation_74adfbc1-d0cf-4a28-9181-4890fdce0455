import pandas as pd
import numpy as np
from utils.disqualified_data import save_disqualified_data
from utils.helper import calculate_centroid, calculate_distances_from_point_market, clean_data, distance_matrix, get_closest_markets_from_farthest_market, isOutlier, market_division, boundary_finder
from utils.minimal_face_change import minimal_face_change
from utils.area_calc import generate_territory_area_and_overlap
from Helper.osrm_helper import is_outlier_OSRM

def territory_optimization(data: pd.DataFrame, DIST_CONVT: float, N_CLUSTER_INITIAL: int, N_TERRITORIES: int, ACCEPTED_STD: float, NUM_CLOSEST: int,COUNTRY:str,OUTLET_COUNT_THRESHOLD:int) -> list:

    # Step 1: Data Cleaning
    cleaned_data,rows_with_zero_lat_long = clean_data(data)
    cleaned_data.reset_index(drop=True, inplace=True)
    
    # Creating distance matrix
    locations = cleaned_data[["Latitude", "Longitude"]].apply(tuple, axis=1)
    np_locations = np.array(locations.tolist())
    
    # Outlier detetction and removal
    if np_locations.shape[0] > OUTLET_COUNT_THRESHOLD:
        dist_matrix = distance_matrix(np_locations, DIST_CONVT)
        cleaned_data = isOutlier(cleaned_data,dist_matrix)
        del dist_matrix
    else:
        # perform osrm distance matrix
        cleaned_data = is_outlier_OSRM(cleaned_data,DIST_CONVT,COUNTRY) 
    
    cleaned_data['Idx'] = range(len(cleaned_data))

    cleaned_data,idx_used,disqualified_df = save_disqualified_data(cleaned_data,rows_with_zero_lat_long)

    # Step 3: Divide the data into sub-territories
    cleaned_data,market_store_loc_and_cnt = market_division(cleaned_data,N_CLUSTER_INITIAL)


    # Initialize an empty list to store the territories
    territory_label = 1
    cleaned_data_clone = cleaned_data.copy()

    # While there are still outlets in cleaned_data
    while cleaned_data_clone.shape[0] > 0 and territory_label < N_TERRITORIES:
        # Step 1: Identify the centroid of the complete data
        global_centroid = calculate_centroid(cleaned_data_clone)
        
        # Step 2: Find the farthest outlet from the centroid
        farthest_market_label = calculate_distances_from_point_market(market_store_loc_and_cnt, global_centroid,DIST_CONVT,minimum=False)
        
        market_labels_for_teritory = get_closest_markets_from_farthest_market(market_store_loc_and_cnt,farthest_market_label,NUM_CLOSEST,cleaned_data_clone,calculate_centroid,calculate_distances_from_point_market ,ACCEPTED_STD,DIST_CONVT)
        cleaned_data.loc[cleaned_data['SubTerritoryId'].isin(market_labels_for_teritory), 'TerritoryId'] = territory_label
        
        territory_label += 1
        # Remove the outlets in the current territory from the cleaned_data for further analysis
        cleaned_data_clone = cleaned_data_clone[~cleaned_data_clone['SubTerritoryId'].isin(market_labels_for_teritory)]

        if market_store_loc_and_cnt == {}:
            break
        
    remaining_labels = list(market_store_loc_and_cnt.keys())
    if remaining_labels:
        cleaned_data.loc[cleaned_data['SubTerritoryId'].isin(remaining_labels), 'TerritoryId'] = territory_label

    
    cleaned_data = cleaned_data[['OutletErpId', 'Latitude', 'Longitude','SubTerritoryId', 'TerritoryId','EmployeeId','TimePerVisit','VisitFrequency']]
    
    cleaned_data_mfc = minimal_face_change(cleaned_data)

    # Generate territory metrics
    territory_area,territory_overlap =generate_territory_area_and_overlap(cleaned_data)
    territory_overlap_dict = pd.concat([territory_overlap.groupby('Territory ID 1')['Overlapping Area (km²)'].sum(), territory_overlap.groupby('Territory ID 2')['Overlapping Area (km²)'].sum()]).groupby(level=0).sum().to_dict()
    territory_area_dict = territory_area.groupby('Territory ID')['Size/Area (km²)'].sum().to_dict()
    
    
    # find territory boundaries and subterritory boundaries
    territory_boundaries = boundary_finder(cleaned_data,territory_overlap_dict,territory_area_dict,sub_territory=False)
    subterritory_boundaries = boundary_finder(cleaned_data,territory_overlap_dict,territory_area_dict,sub_territory=True)
    
    

    # Combining necessary data
    with pd.ExcelWriter(f'output.xlsx', engine='openpyxl') as writer:
            if not cleaned_data_mfc.empty:  
                cleaned_data_mfc.to_excel(writer, sheet_name='territories', index=False)
            
            if not disqualified_df.empty:
                disqualified_df.to_excel(writer, sheet_name='DQ', index=False)
            
            # As per current scope the following commented snippet is not required
            # if not territory_area.empty:
            #     territory_area.to_excel(writer, sheet_name='Territory Area', index=False)
            
            # if not territory_overlap.empty:
            #     territory_overlap.to_excel(writer, sheet_name='Territory Overlap', index=False)  

    

    print("Territory generation completed")          

    return cleaned_data_mfc,territory_boundaries,subterritory_boundaries
