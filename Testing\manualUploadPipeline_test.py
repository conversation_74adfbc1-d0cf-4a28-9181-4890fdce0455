import time
import os
import sys
import asyncio
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)
import warnings
from ManualUploadPipeline.function_app import process_manual_upload

if __name__ == "__main__":
    warnings.filterwarnings("ignore")
    start_time = time.time()
    print("Start Time: " + time.ctime(start_time))
    asyncio.run(process_manual_upload("workaround-territory-optimization/file.xlsx"))
    end_time = time.time()
    print("End Time: "+time.ctime(start_time))
    total_time = end_time - start_time
    print(f"Total Time Duration: {total_time/60} min")