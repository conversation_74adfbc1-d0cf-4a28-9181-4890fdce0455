import numpy as np
import pandas as pd
from haversine import haversine
from scipy.spatial import ConvexHull
from sklearn.cluster import KMeans
from sklearn.metrics import calinski_harabasz_score as ch_scr

# Function to calculate the centroid of given outlets based on latitude and longitude
def calculate_centroid(data: pd.DataFrame) -> tuple[float, float]:
    lat_mean = data['Latitude'].mean()
    long_mean = data['Longitude'].mean()
    return (lat_mean, long_mean)

# Function to calculate the distance between two points (lat1, long1) and (lat2, long2)[Market level]
def calculate_distances_from_point_market(market_dict: dict, point: tuple[float, float],DIST_CONVT: float,minimum = False) -> int:
    # Convert market_dict to DataFrame for vectorized operations
    market_df = pd.DataFrame(
        [(label, *market_dict[label][0]) for label in market_dict],
        columns=['label', 'lat', 'lon']
    )
    
    # Calculate distances using vectorized operations
    distances = market_df.apply(
        lambda row: haversine((row['lat'], row['lon']), point, unit='km') * DIST_CONVT,
        axis=1
    )
    

    if minimum:
        # Find the index of the minimum distance
        min_idx = distances.idxmin()
        
        return market_df.loc[min_idx, 'label']
    else:
        # Find the index of the maximum distance
        max_idx = distances.idxmax()
        
        return market_df.loc[max_idx, 'label']

def clean_data(data: pd.DataFrame) -> pd.DataFrame:
    
    # 1. Check data types and remove rows with incorrect data types.
    # Ensure Latitude and Longitude are floats, Time per visit and Frequency are numeric
    data['Latitude'] = pd.to_numeric(data['Latitude'], errors='coerce')
    data['Longitude'] = pd.to_numeric(data['Longitude'], errors='coerce')
    data['TimePerVisit'] = pd.to_numeric(data['TimePerVisit'], errors='coerce')
    data['VisitFrequency'] = data['VisitFrequency'].astype(int)
    data['OutletErpId'] = data['OutletErpId'].astype(str)
    data['EmployeeId'] = data['EmployeeId'].astype(str)
    
    # 1.5 Prefixing 0s in the outletErpid so that it is always of 10 digits and employeeId so that it is always of 6 digits
    data['OutletErpId'] = data['OutletErpId'].apply(lambda x: x.zfill(10))
    data['EmployeeId'] = data['EmployeeId'].apply(lambda x: x.zfill(6))
    
    # 2. Remove rows with any null values
    data = data.dropna()

    # 3. Remove duplicate Outlet ERP IDs, keeping the first occurrence
    data = data.drop_duplicates(subset='OutletErpId', keep='first')

    # 3.3 Remove 0 Latitude and Longitude values
    rows_with_zero_lat_long = data[(data['Latitude'] == 0) & (data['Longitude'] == 0)]
    data = data[~(data['Latitude'] == 0) & ~(data['Longitude'] == 0)]
    
    # 3.5 shifting values of  duplicates based on Latitude and Longitude
    coords = data.groupby(['Latitude', 'Longitude']).size().reset_index(name ='count')
    coords_with_duplicates = coords[coords['count'] > 1]

    for _, row in coords_with_duplicates.iterrows():
        lat,lon = row['Latitude'], row['Longitude']
        duplicate_indices = data[(data['Latitude'] == lat) & (data['Longitude'] == lon)].index[1:]

        for idx in duplicate_indices:
            data.loc[idx, 'Latitude'] += np.random.uniform(-0.0001, 0.0001)
            data.loc[idx, 'Longitude'] += np.random.uniform(-0.0001, 0.0001)


    # 4. Remove completely duplicate rows (where all columns are identical)
    data = data.drop_duplicates()

    # Return the cleaned DataFrame
    return data, rows_with_zero_lat_long

def clean_data_updation(df: pd.DataFrame) -> pd.DataFrame:
    df['OutletErpId'] = df['OutletErpId'].astype(str)
    df['OutletErpId'] = df['OutletErpId'].apply(lambda x: x.zfill(10))
    
    df['EmployeeId'] = df['EmployeeId'].astype(str)
    df['EmployeeId'] = df['EmployeeId'].apply(lambda x: x.zfill(6))
    
    df['TerritoryId'] = df['TerritoryId'].astype(str)
    df['VisitFrequency'] = df['VisitFrequency'].astype(int)
    
    df['TimePerVisit'] = df['TimePerVisit'].astype(float)
    
    df['Latitude'] = df['Latitude'].astype(float)
    df['Longitude'] = df['Longitude'].astype(float)
    return df

def isOutlier(
    df: pd.DataFrame,dis_matrix
):  # DATAFRAME THAT IS BEING FED SHOULD HAVE A RESETTED IDX
    if len(df) <= 5:
        df['IsOutlier'] = False
        return df
    
    # Creating threshold mean+(std*3)
    total_mean = dis_matrix.mean()
    std_dev = dis_matrix.std() * 2
    threshold = total_mean + std_dev

    # distance matrix where distance greater than threshold is true
    outlier_store = dis_matrix > threshold

    # getting ids for stores where outliers are greater than 50% of the store universe
    outlier_idx = []
    for idx, outlier in enumerate(outlier_store):
        total_outlier = outlier.sum()
        if total_outlier > ((outlier_store.shape[0] - 1)/2):
            outlier_idx.append(idx)

    # creating a column that shows which store is an outlier
    df["IsOutlier"] = False
    for idx in outlier_idx:
        df.loc[idx, "IsOutlier"] = True

    return df

def distance_matrix(locations, dist_convert):
    # Number of locations
    num_locations = len(locations)
    
    # Initialize the distance matrix with zeros
    distance_matrix = np.zeros((num_locations, num_locations))

    # Use numpy broadcasting to calculate pairwise distances efficiently
    for i in range(num_locations):
        for j in range(i + 1, num_locations):  # Calculate only upper triangle
            distance = haversine(locations[i], locations[j], unit="km") * dist_convert
            distance_matrix[i, j] = distance
            distance_matrix[j, i] = distance  # Exploit symmetry

    return distance_matrix

# calculate distances from the farthest market to all other markets.
def get_closest_markets_from_farthest_market(market_store_loc_and_cnt,farthest_market_label,NUM_CLOSEST,cleaned_data_clone,calculate_centroid,calculate_distances_from_point_market ,ACCEPTED_STD,DIST_CONVT) -> list:
    
    farthest_market_loc = market_store_loc_and_cnt[farthest_market_label][0]
    
    count = 0
    market_labels_selected = []
    while count < NUM_CLOSEST:
        if len(market_labels_selected) == 0:
            farthest_centroid = farthest_market_loc
            market_labels_selected.append(farthest_market_label)
            cnt = market_store_loc_and_cnt[farthest_market_label][1]
            count += cnt
            market_store_loc_and_cnt.pop(farthest_market_label)
        else:
            markets_selected_df = cleaned_data_clone[cleaned_data_clone['SubTerritoryId'].isin(market_labels_selected)]
            farthest_centroid = calculate_centroid(markets_selected_df)
        
        if market_store_loc_and_cnt == {}:
            return market_labels_selected

        nearest_label = calculate_distances_from_point_market(market_store_loc_and_cnt, farthest_centroid,DIST_CONVT,minimum=True)
        cnt = market_store_loc_and_cnt[nearest_label][1]
        if count+cnt <= NUM_CLOSEST:
            market_labels_selected.append(nearest_label)
            market_store_loc_and_cnt.pop(nearest_label)
            count += cnt
        else:
            break

    current_std = np.std([count,NUM_CLOSEST])
    while True:
        markets_selected_df = cleaned_data_clone[cleaned_data_clone['SubTerritoryId'].isin(market_labels_selected)]
        farthest_centroid = calculate_centroid(markets_selected_df)
        nearest_label = calculate_distances_from_point_market(market_store_loc_and_cnt, farthest_centroid,DIST_CONVT,minimum=True)
        cnt = market_store_loc_and_cnt[nearest_label][1]
        count += cnt
        
        new_std = np.std([count,NUM_CLOSEST])
        if new_std < current_std and new_std <= ACCEPTED_STD:
            market_labels_selected.append(nearest_label)
            market_store_loc_and_cnt.pop(nearest_label)
            if market_store_loc_and_cnt == {}:
                return market_labels_selected
            current_std = new_std
        else:
            break
        
    
    return market_labels_selected

def market_division(df: pd.DataFrame,cluster_count: int) -> pd.DataFrame:

    # Ensure we have at least 2 points for clustering
    if len(df) < 2:
        df['SubTerritoryId'] = 0
        market_store_cnt = {0: [df[['Latitude','Longitude']].mean(), len(df)]}
        return df, market_store_cnt

    # Calculate maximum possible clusters (n_samples - 1 or cluster_count, whichever is smaller)
    max_possible_clusters = min(cluster_count, len(df) - 1)

    # Ensure we have at least 2 clusters if possible
    if max_possible_clusters < 2:
        df['SubTerritoryId'] = 0
        market_store_cnt = {0: [df[['Latitude','Longitude']].mean(), len(df)]}
        return df, market_store_cnt
    
    # Adjust cluster_count to be within valid range
    adjusted_cluster_count = min(max(2, cluster_count), max_possible_clusters)
    
   # If only 2 clusters possible, skip the scoring
    if adjusted_cluster_count == 2:
        kmeans = KMeans(n_clusters=2, n_init=10)
        kmeans.fit(df[['Latitude','Longitude']])

    else:
        ch_score_lis = []
        for n_cluster in range(2,adjusted_cluster_count+1):
            kmeans = KMeans(n_clusters=n_cluster)
            kmeans.fit(df[['Latitude','Longitude']])
            ch_score = ch_scr(df[['Latitude','Longitude']],kmeans.labels_)
            ch_score_lis.append(ch_score)
    
        # Find the optimal number of clusters
        optimal_cluster = np.argmax(ch_score_lis) + 2
    
        # Apply KMeans with the optimal number of clusters
        kmeans = KMeans(n_clusters=optimal_cluster)
        kmeans.fit(df[['Latitude','Longitude']])
    df['SubTerritoryId'] = kmeans.labels_

    market_store_cnt = {i: [df[df['SubTerritoryId'] == i][['Latitude','Longitude']].mean(),(df['SubTerritoryId'] == i).sum()] for i in kmeans.labels_}

    return df,market_store_cnt
    
def boundary_finder(territory_df:pd.DataFrame,territory_overlap_dict:dict,territory_area_dict:dict,sub_territory: bool) -> dict:

    def get_convex_hull(points):
        if len(points) < 3:
            return []  # ConvexHull requires at least 3 points
        hull = ConvexHull(points)
        return [(points[vertex][0], points[vertex][1]) for vertex in hull.vertices]

    # Group by territory and find boundaries
    territory_boundaries = {}
    
    if sub_territory:
        # Group by subterritory and find boundaries
        for subterritory, group in territory_df.groupby('SubTerritoryId'):
            market_points = group[['Latitude', 'Longitude']].values
            boundary_points = get_convex_hull(market_points)
            territory_boundaries[subterritory] = boundary_points
    else:
        # Group by territory and find boundaries
        for territory, group in territory_df.groupby('TerritoryId'):
            territory_points = group[['Latitude', 'Longitude']].values
            total_visits = int(group['VisitFrequency'].sum())
            total_retail_time = float(group['TimePerVisit'].sum())
            boundary_points = get_convex_hull(territory_points)
            territory_boundaries[territory] = {
                "Boundaries": boundary_points,
                "TerritoryArea": round(territory_area_dict.get(territory,0),3),
                "TerritoryOverlapArea": round(territory_overlap_dict.get(territory,0),3),
                "TerritoryVisitFrequency": total_visits,
                "TerritoryTotalRetailTime": total_retail_time
            }
            
    return territory_boundaries

def boundary_updater(territory_df:pd.DataFrame,changes_dict:dict,territory_boundaries:dict) -> dict:

    def get_convex_hull(points):
        if len(points) < 3:
            return []  # ConvexHull requires at least 3 points
        hull = ConvexHull(points)
        return [(points[vertex][0], points[vertex][1]) for vertex in hull.vertices]

    # Group by territory and find boundaries
    for territory in changes_dict:
        if changes_dict[territory]["IsDeleted"]:
            territory_boundaries.pop(territory)
            continue
        territory_points = territory_df[territory_df['TerritoryId'] == territory][['Latitude', 'Longitude']].values
        total_visits = int(territory_df[territory_df['TerritoryId'] == territory]['VisitFrequency'].sum())
        total_retail_time = float(territory_df[territory_df['TerritoryId'] == territory]['TimePerVisit'].sum())
        if len(changes_dict[territory]['Outlets']) > 0:
            boundary_points = get_convex_hull(territory_points)
            territory_boundaries[territory]["Boundaries"] = boundary_points
        territory_boundaries[territory]["TerritoryArea"] = round(changes_dict[territory]["TerritoryArea"],3)
        territory_boundaries[territory]["TerritoryOverlapArea"] = round(changes_dict[territory]["TerritoryOverlapArea"],3)
        territory_boundaries[territory]["TerritoryVisitFrequency"] = total_visits
        territory_boundaries[territory]["TerritoryTotalRetailTime"] = total_retail_time        
    return territory_boundaries


def update_territory(territory_df, territory_changes):
    for territory_key in territory_changes:
        outlets = territory_changes[territory_key]['Outlets']
        mask = territory_df['OutletErpId'].isin(outlets)
        if mask.any():
            territory_df.loc[mask, 'TerritoryId'] = territory_key
    return territory_df