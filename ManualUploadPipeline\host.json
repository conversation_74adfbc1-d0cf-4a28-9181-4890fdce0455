{"version": "2.0", "concurrency": {"dynamicConcurrencyEnabled": true, "snapshotPersistenceEnabled": true}, "logging": {"logLevel": {"default": "Error", "Function": "Information"}}, "extensions": {"blobs": {"maxDegreeOfParallelism": 1}}, "retry": {"strategy": "exponentialBackoff", "maxRetryCount": 3, "minimumInterval": "00:00:05", "maximumInterval": "00:01:00"}, "functionTimeout": "00:30:00", "extensionBundle": {"id": "Microsoft.Azure.Functions.ExtensionBundle", "version": "[4.*, 5.0.0)"}}