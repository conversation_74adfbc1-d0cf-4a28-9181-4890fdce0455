import pandas as pd
from scipy.spatial import ConvexHull
from shapely.geometry import Polygon
from geopy.distance import geodesic
import geopandas as gpd
from shapely.ops import transform
import pyproj

def calculate_polygon_area(polygon):
    """
    Calculate the area of a polygon using geodesic distances.
    Returns area in square kilometers.
    """
    coords = list(polygon.exterior.coords)
    area = 0

    for i in range(len(coords) - 1):
        lon1, lat1 = coords[i]  # Coordinates are now in lon/lat order
        lon2, lat2 = coords[i + 1]
        base = geodesic((lat1, lon1), (lat2, lon1)).meters  # Note: geodesic still expects (lat, lon)
        height = geodesic((lat1, lon1), (lat1, lon2)).meters
        area += (base * height) / 2  # Triangle approximation

    return area / 1e6  # Convert square meters to square kilometers

def calculate_overlapping_area(poly1, poly2, crs="EPSG:3857"):
    """
    Compute accurate overlapping area by reprojecting polygons to an equal-area projection.
    
    :param poly1: First Shapely Polygon (in latitude/longitude)
    :param poly2: Second Shapely Polygon (in latitude/longitude)
    :param crs: Coordinate reference system (default is Web Mercator EPSG:3857)
    :return: Overlapping area in square kilometers
    """
    # Create GeoDataFrame for both polygons
    gdf = gpd.GeoDataFrame({'geometry': [poly1, poly2]}, crs="EPSG:4326")  # WGS84 (lat/lon)
    
    # Reproject to an equal-area projection (Web Mercator EPSG:3857)
    gdf = gdf.to_crs(crs)

    # Compute intersection
    intersection = gdf.iloc[0].geometry.intersection(gdf.iloc[1].geometry)

    # Return area in square kilometers
    return intersection.area / 1e6  # Convert square meters to km²

def generate_territory_area_and_overlap(sheet1_data: pd.DataFrame):
    """
    Function to calculate the area of each territory and the overlapping area between territories.
    
    Args:
    sheet1_data (pd.DataFrame): The input DataFrame containing the data.
    output_file (str): The name of the output Excel file to write the metrics.
    """
    # Strip extra spaces from column names for consistency
    sheet1_data.columns = sheet1_data.columns.str.strip()
    
    # Function to generate convex hulls
    def get_convex_hull(points):
        unique_points = set(map(tuple, points))
        if len(unique_points) < 3:
            return None  # ConvexHull requires at least 3 distinct points
        
        hull = ConvexHull(points)
        # Swap lat/long to long/lat for proper transformation
        return [(points[vertex][1], points[vertex][0]) for vertex in hull.vertices]
    
    # Group outlets by territory
    territories = sheet1_data.groupby('TerritoryId')

    # Data storage for metrics
    territory_areas = []
    hull_polygons = {}  # Store hull polygons for overlap calculation

    # Add elements to the map
    for territory_id, outlets in territories:
        # Extract coordinates for the outlets in the territory
        coords = outlets[['Latitude', 'Longitude']].values

        # Compute convex hull
        hull_points = get_convex_hull(coords)

        if hull_points:
            # Close the polygon for plotting
            hull_points.append(hull_points[0])
            
            # Convert to Shapely Polygon (already in long/lat order)
            hull_polygon = Polygon(hull_points)
            hull_polygons[territory_id] = hull_polygon

            # Compute area in square kilometers
            area_km2 = calculate_polygon_area(hull_polygon)
            territory_areas.append({"Territory ID": territory_id, "Size/Area (km²)": area_km2})
    
    # Compute territory overlap
    territory_overlaps = []
    territory_ids = list(hull_polygons.keys())

    # Create the projector for Web Mercator
    projector = pyproj.Transformer.from_crs("EPSG:4326", "EPSG:3857", always_xy=True).transform

    for i in range(len(territory_ids)):
        for j in range(i + 1, len(territory_ids)):  # Avoid duplicate comparisons
            t1, t2 = territory_ids[i], territory_ids[j]
            poly1, poly2 = hull_polygons[t1], hull_polygons[t2]
            intersection = poly1.intersection(poly2)
            if not intersection.is_empty:
                # Transform to Web Mercator for area calculation
                intersection_proj = transform(projector, intersection)
                area_km2 = intersection_proj.area / 1e6
                
                territory_overlaps.append({
                    "Territory ID 1": t1,
                    "Territory ID 2": t2,
                    "Overlapping Area (km²)": round(area_km2,4)
                })
            else:
                territory_overlaps.append({
                    "Territory ID 1": t1,
                    "Territory ID 2": t2,
                    "Overlapping Area (km²)": 0.0
                })

    # Convert metrics to DataFrames
    df_territory_area = pd.DataFrame(territory_areas)
    df_territory_overlap = pd.DataFrame(territory_overlaps)

    return df_territory_area, df_territory_overlap

    return df_territory_area, df_territory_overlap