"""
Migration Status Checker for Territory Optimization Data

This utility script provides status reports on the migration progress
and can help identify which folders need migration or have issues.
"""

import os
import sys
import logging
from typing import Dict, List, Tuple
import json

# Add parent directory for imports
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)

from Helper.blob_helper import (
    initialize_blob_service_client,
    get_container_client,
    read_json_from_blob,
    read_blob_to_dataframe_excel
)
from global_constant import CONTAINER_NAME, STORAGE_CONN_STRING

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class MigrationStatusChecker:
    """Check and report on migration status of territory optimization data."""
    
    def __init__(self):
        self.blob_service_client = initialize_blob_service_client(STORAGE_CONN_STRING)
        self.container_client = get_container_client(self.blob_service_client, CONTAINER_NAME)
    
    def discover_all_table_ids(self) -> List[str]:
        """Discover all table_id folders in the container."""
        table_ids = set()
        blob_list = self.container_client.list_blobs()
        
        for blob in blob_list:
            blob_path = blob.name
            if '/' in blob_path:
                potential_table_id = blob_path.split('/')[0]
                if potential_table_id.isdigit():
                    table_ids.add(potential_table_id)
        
        return sorted(list(table_ids))
    
    def check_migration_status(self, table_id: str) -> Dict:
        """
        Check detailed migration status for a single table_id.
        
        Returns:
            Dictionary with migration status details
        """
        status = {
            'table_id': table_id,
            'is_migrated': False,
            'has_entity_list': False,
            'has_entity_folder': False,
            'excel_files_with_entity_id': [],
            'excel_files_without_entity_id': [],
            'json_files_migrated': [],
            'json_files_not_migrated': [],
            'issues': []
        }
        
        try:
            # Check for entity_list.json
            entity_list_path = f"{table_id}/entity_list.json"
            blob_client = self.blob_service_client.get_blob_client(
                container=CONTAINER_NAME, blob=entity_list_path
            )
            
            if blob_client.exists():
                status['has_entity_list'] = True
                try:
                    entity_list = read_json_from_blob(
                        self.blob_service_client, CONTAINER_NAME, entity_list_path
                    )
                    if isinstance(entity_list, list) and "Entity1" in entity_list:
                        status['is_migrated'] = True
                except Exception as e:
                    status['issues'].append(f"Invalid entity_list.json: {str(e)}")
            
            # Check for Entity1 folder
            entity_blobs = list(self.container_client.list_blobs(name_starts_with=f"{table_id}/Entity1/"))
            if entity_blobs:
                status['has_entity_folder'] = True
                status['is_migrated'] = True
            
            # Check Excel files for EntityId column
            excel_blobs = list(self.container_client.list_blobs(name_starts_with=f"{table_id}/"))
            for blob in excel_blobs:
                if blob.name.endswith('.xlsx') and blob.name.count('/') == 1:  # Direct files only
                    try:
                        df = read_blob_to_dataframe_excel(
                            self.blob_service_client, CONTAINER_NAME, blob.name
                        )
                        if 'EntityId' in df.columns:
                            status['excel_files_with_entity_id'].append(blob.name)
                        else:
                            status['excel_files_without_entity_id'].append(blob.name)
                    except Exception as e:
                        status['issues'].append(f"Cannot read {blob.name}: {str(e)}")
            
            # Check JSON file migration status
            json_files_old = {
                'territory_boundaries': f"{table_id}/territory_boundaries.json",
                'subterritory_boundaries': f"{table_id}/subterritory_boundaries.json"
            }
            
            json_files_new = {
                'territory_boundaries': f"{table_id}/Entity1/territory_boundaries.json",
                'subterritory_boundaries': f"{table_id}/Entity1/subterritory_boundaries.json"
            }
            
            for file_type, old_path in json_files_old.items():
                new_path = json_files_new[file_type]
                
                old_exists = self.blob_service_client.get_blob_client(
                    container=CONTAINER_NAME, blob=old_path
                ).exists()
                
                new_exists = self.blob_service_client.get_blob_client(
                    container=CONTAINER_NAME, blob=new_path
                ).exists()
                
                if new_exists:
                    status['json_files_migrated'].append(file_type)
                elif old_exists:
                    status['json_files_not_migrated'].append(file_type)
            
        except Exception as e:
            status['issues'].append(f"Error checking status: {str(e)}")
        
        return status
    
    def generate_migration_report(self, table_ids: List[str] = None) -> Dict:
        """
        Generate comprehensive migration report.
        
        Args:
            table_ids: List of specific table_ids to check, or None for all
            
        Returns:
            Dictionary with migration report
        """
        if table_ids is None:
            table_ids = self.discover_all_table_ids()
        
        report = {
            'total_folders': len(table_ids),
            'migrated_folders': 0,
            'not_migrated_folders': 0,
            'partially_migrated_folders': 0,
            'folders_with_issues': 0,
            'detailed_status': [],
            'summary': {
                'fully_migrated': [],
                'not_migrated': [],
                'partially_migrated': [],
                'with_issues': []
            }
        }
        
        logging.info(f"Checking migration status for {len(table_ids)} folders...")
        
        for i, table_id in enumerate(table_ids, 1):
            if i % 10 == 0:
                logging.info(f"Processed {i}/{len(table_ids)} folders...")
            
            status = self.check_migration_status(table_id)
            report['detailed_status'].append(status)
            
            # Categorize status
            if status['issues']:
                report['folders_with_issues'] += 1
                report['summary']['with_issues'].append(table_id)
            elif status['is_migrated'] and status['has_entity_list'] and status['has_entity_folder']:
                report['migrated_folders'] += 1
                report['summary']['fully_migrated'].append(table_id)
            elif status['has_entity_list'] or status['has_entity_folder'] or status['excel_files_with_entity_id']:
                report['partially_migrated_folders'] += 1
                report['summary']['partially_migrated'].append(table_id)
            else:
                report['not_migrated_folders'] += 1
                report['summary']['not_migrated'].append(table_id)
        
        return report
    
    def print_summary_report(self, report: Dict):
        """Print a human-readable summary of the migration report."""
        print("\n" + "=" * 60)
        print("TERRITORY OPTIMIZATION MIGRATION STATUS REPORT")
        print("=" * 60)
        
        print(f"Total folders: {report['total_folders']}")
        print(f"Fully migrated: {report['migrated_folders']}")
        print(f"Not migrated: {report['not_migrated_folders']}")
        print(f"Partially migrated: {report['partially_migrated_folders']}")
        print(f"With issues: {report['folders_with_issues']}")
        
        print(f"\nMigration progress: {(report['migrated_folders']/report['total_folders']*100):.1f}%")
        
        if report['summary']['not_migrated']:
            print(f"\nFolders needing migration ({len(report['summary']['not_migrated'])}):")
            for table_id in report['summary']['not_migrated'][:10]:  # Show first 10
                print(f"  - {table_id}")
            if len(report['summary']['not_migrated']) > 10:
                print(f"  ... and {len(report['summary']['not_migrated']) - 10} more")
        
        if report['summary']['with_issues']:
            print(f"\nFolders with issues ({len(report['summary']['with_issues'])}):")
            for table_id in report['summary']['with_issues'][:5]:  # Show first 5
                print(f"  - {table_id}")
            if len(report['summary']['with_issues']) > 5:
                print(f"  ... and {len(report['summary']['with_issues']) - 5} more")
        
        print("\n" + "=" * 60)
    
    def save_detailed_report(self, report: Dict, filename: str = "migration_status_report.json"):
        """Save detailed report to JSON file."""
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        logging.info(f"Detailed report saved to {filename}")

def main():
    """Main function to run migration status check."""
    logging.info("Starting Migration Status Check")
    
    try:
        checker = MigrationStatusChecker()
        
        # Generate report for all folders
        report = checker.generate_migration_report()
        
        # Print summary
        checker.print_summary_report(report)
        
        # Save detailed report
        checker.save_detailed_report(report)
        
        logging.info("Migration status check completed")
        
    except Exception as e:
        logging.error(f"Error in migration status check: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
