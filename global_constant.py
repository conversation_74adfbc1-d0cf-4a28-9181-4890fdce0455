import os

STORAGE_CONN_STRING = os.environ.get("FaiDataLakeConnectionString") #This should be removed from the project #'DefaultEndpointsProtocol=https;AccountName=faiadls;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net'
STORAGE_CONN_STRING_QUEUE = os.environ.get("AzureWebJobsStorageConnectionString") #This should be removed from the project #"DefaultEndpointsProtocol=https;AccountName=faappapiv3;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
CONTAINER_NAME = 'territory-optimization'
QUEUE_NAME_ERROR = 'fai-status-updation-queue'
DIST_CONVT = 1.5
TABLE_NAME = 'TerritoryOptimizationDetails'
BOUNDARY_FILES_PATH = "boundary_files"
OSRM_BASE_URL = "https://osrm.fieldassist.io"
OUTLET_COUNT_THRESHOLD = 15000
SPEED = 20

