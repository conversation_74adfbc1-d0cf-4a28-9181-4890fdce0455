import pandas as pd
def minimal_face_change(df: pd.DataFrame):
   # Step 1: Create the N x M matrix of territory-to-outlet mapping
    territories = df['TerritoryId'].unique()
    employees = df['EmployeeId'].unique()

    # Create a mapping of which employee is assigned to which outlet
    outlet_employee_mapping = df.groupby(['TerritoryId', 'EmployeeId'])['OutletErpId'].count().unstack(fill_value=0)

    # Step 2: Initialize a list to track which employees have been assigned
    assigned_employees = set()

    # Step 3: Assign employees based on the most matching outlets per territory
    territory_employee_assignment = {}

    for territory in territories:
        # Get employees sorted by the number of outlets they are assigned to in the territory
        sorted_employees = outlet_employee_mapping.loc[territory].sort_values(ascending=False)
        
        # Select the first available employee who has the most outlets in this territory
        for employee in sorted_employees.index:
            if employee not in assigned_employees:
                # Assign this employee to the territory
                territory_employee_assignment[territory] = employee
                # Mark the employee as assigned
                assigned_employees.add(employee)
                break

    # Step 4: Handle edge cases (New Territories > Unique Employees or vice versa)
    # Scenario 1: New Territories > Unique Employees
    if len(territories) > len(employees):
        for i in range(len(employees), len(territories)):
            # Add "New EMP" placeholder employees to remaining territories
            territory_employee_assignment[territories[i]] = f"New EMP - {i+1}"

    # # Scenario 2: New Territories < Unique Employees
    # elif len(territories) < len(employees):
    #     # Discard employees who are not assigned to any territory
    #     remaining_employees = employees[len(territories):]
    #     for employee in remaining_employees:
    #         # If an employee is not assigned to any territory, discard them
    #         assigned_employees.discard(employee)

    df['EmployeeId'] = df['TerritoryId'].apply(lambda x: territory_employee_assignment.get(x, None))
    df['TerritoryId'] = df['TerritoryId'].astype(int)
    # Return the modified DataFrame
    return df