# Manual Upload Pipeline Azure Function

An Azure Function that processes manual territory optimization uploads through blob triggers with comprehensive error handling and retry mechanisms.

## Overview

The Manual Upload Pipeline is a serverless Azure Function designed to handle manual file uploads for territory optimization processing. It automatically processes Excel files uploaded to Azure Blob Storage, validates the data, creates database records, organizes files, and generates territory boundaries and outlet data structures.

## Function Details

### Function Specifications
- **Function Name**: `Manual_Upload_Pipeline`
- **Trigger Type**: Blob Trigger
- **Trigger Path**: `manual-uploads/{name}`
- **Runtime**: Python 3.9+
- **Timeout**: 30 minutes
- **Concurrency**: Dynamic (managed by Azure Functions)

### Input Requirements

#### File Format
- **Supported Format**: Excel files (.xlsx, .xls)
- **Upload Location**: `manual-uploads/` container in Azure Blob Storage
- **File Naming**: Any valid filename (original name preserved)

#### Required Columns
The uploaded Excel file must contain the following columns:
- `OutletErpId` - Unique identifier for outlets
- `Latitude` - Outlet latitude coordinates
- `Longitude` - Outlet longitude coordinates
- `SubTerritoryId` - Sub-territory identifier
- `TerritoryId` - Territory identifier
- `EmployeeId` - Employee identifier
- `TimePerVisit` - Time required per visit (minutes)
- `VisitFrequency` - Visit frequency
- `EntityId` - Entity identifier for grouping

## Processing Pipeline

The function follows a structured 3-step processing pipeline:

### Step 1: Database Record Creation
- Creates a tracking record in `TerritoryOptimizationDetails` table
- Assigns unique `table_id` for process tracking
- Sets initial status to "Manual upload processing started"
- Records processing metadata (user info, timestamps, file details)

### Step 2: File Storage Operations
- Copies uploaded file to `territory-optimization/{table_id}/` container
- Preserves original filename for reference
- Creates standardized copy as `output.xlsx` for downstream processing
- Organizes files using unique table_id for isolation

### Step 3: Entity-Based Data Processing
- Groups data by `EntityId` for entity-specific processing
- Calculates territory area and overlap metrics
- Generates territory boundaries using `boundary_finder` algorithm
- Creates organized folder structure:
  ```
  territory-optimization/{table_id}/
  ├── {entity_id}_territory_boundaries.json
  ├── {entity_id}/
  │   ├── territory_boundaries.json
  │   ├── subterritory_boundaries.json
  │   └── Territory/
  │       └── {territory_id}/
  │           └── territory_outlet.json
  ```

## Output Structure

### Generated Files
1. **Territory Boundaries**: `{entity_id}_territory_boundaries.json`
2. **Subterritory Boundaries**: `{entity_id}/subterritory_boundaries.json`
3. **Territory Outlets**: `{entity_id}/Territory/{territory_id}/territory_outlet.json`
4. **File Copies**: Original file and `output.xlsx`

### File Contents
- **Boundary Files**: GeoJSON-formatted territory boundaries with convex hull calculations
- **Outlet Files**: JSON arrays containing outlet data with coordinates and visit information
- **Processing Files**: Preserved input files for audit and reprocessing

## Error Handling & Retry Mechanism

### Retry Configuration
- **Strategy**: Exponential backoff
- **Maximum Retries**: 3 attempts
- **Minimum Interval**: 5 seconds
- **Maximum Interval**: 1 minute

### Error Types
1. **Validation Errors**: Missing columns, empty files, invalid data formats
2. **Processing Errors**: Database failures, blob storage issues, calculation errors
3. **System Errors**: Timeout, memory, or connectivity issues

### Error Notifications
- Errors are sent to processing queue for monitoring
- Detailed error logging with context and timestamps
- Sentry integration for error tracking and alerting

## Dependencies

### Python Packages
```
azure-functions>=1.11.0
azure-storage-queue>=12.6.0
azure-storage-blob>=12.14.0
azure-storage-file-datalake>=12.9.0
numpy>=1.21.0
pandas>=1.3.0
openpyxl>=3.0.9
sentry_sdk>=1.5.0
configparser>=5.2.0
aiodbc>=0.4.0
```

### External Services
- Azure Blob Storage (file storage)
- Azure Queue Storage (notifications)
- Azure SQL Database (tracking records)
- Sentry (error monitoring)

## Configuration

### Environment Variables
```bash
# Storage Configuration
STORAGE_CONN_STRING=<Azure Storage Connection String>
CONTAINER_NAME=territory-optimization
QUEUE_NAME_ERROR=processing-errors

# Database Configuration
TABLE_NAME=TerritoryOptimizationDetails

# Monitoring Configuration
AppSettings__Deployment=<environment>
```

### Container Configuration
- **Source Container**: `manual-uploads`
- **Processing Container**: `territory-optimization`
- **Queue**: `processing-errors`

## Testing

### Test File Location
The test suite is located at:
```
Testing/manualUploadPipeline_test.py
```

### Running Tests

#### Prerequisites
1. Ensure all dependencies are installed:
   ```bash
   pip install -r requirements.txt
   ```

2. Set up test environment (optional - tests use mocks by default):
   ```bash
   # Only needed for integration testing
   export STORAGE_CONN_STRING="your_test_storage_connection"
   ```

#### Execute Tests
```bash
# Navigate to the Testing directory
cd Testing

# Run the manual upload pipeline test
python manualUploadPipeline_test.py
```

### Test Features

The test suite includes:

1. **Mock Data Generation**:
   - Creates realistic Excel files with required columns
   - Generates sample territory and outlet data
   - Simulates various data scenarios

2. **Azure Services Mocking**:
   - Mocks Azure Blob Storage operations
   - Mocks Azure Queue Service interactions
   - Mocks database operations
   - Prevents actual cloud resource usage during testing

3. **Comprehensive Validation**:
   - Verifies all processing steps execute correctly
   - Validates function call sequences
   - Checks error handling scenarios
   - Measures performance metrics

4. **Test Output**:
   ```
   🧪 MANUAL UPLOAD PIPELINE TEST
   ============================================================
   Start Time: Mon Jan 15 10:30:00 2024
   Creating test Excel file with sample data...
   Setting up mock blob input stream...
   Running manual upload pipeline test...
   ✅ Manual upload pipeline test completed successfully!
   
   📊 Test Verification:
      - Database insert called: True
      - Blob operations performed: True
      - Territory metrics calculated: True
      - Boundary finder executed: True
      - JSON files uploaded: True
   
   ============================================================
   End Time: Mon Jan 15 10:30:15 2024
   Total Test Duration: 15.23 seconds (0.25 minutes)
   
   🎉 TEST RESULT: PASSED
   ============================================================
   ```

### Test Data Structure

The test creates sample data with:
- **5 outlets** across **3 territories** and **2 entities**
- **Required columns** with realistic coordinate data
- **Territory relationships** for boundary calculations
- **Employee assignments** and visit scheduling data

## Deployment

### Prerequisites
1. Azure Function App with Python 3.9+ runtime
2. Azure Storage Account with blob and queue services
3. Azure SQL Database for tracking records
4. Sentry account for error monitoring (optional)

### Deployment Steps

1. **Package Dependencies**:
   ```bash
   pip install -r requirements.txt --target .python_packages/lib/site-packages
   ```

2. **Configure Environment**:
   - Set storage connection strings
   - Configure database connections
   - Set up Sentry DSN (optional)

3. **Deploy Function**:
   ```bash
   func azure functionapp publish <function-app-name>
   ```

4. **Verify Deployment**:
   - Check function logs in Azure Portal
   - Test with sample file upload
   - Monitor error queues

## Monitoring

### Logging
- Comprehensive logging at INFO level
- Structured log messages with processing context
- Step-by-step progress tracking
- Error details with stack traces

### Metrics
- Processing duration per file
- Success/failure rates
- Entity and territory counts
- File size and record counts

### Alerts
- Function execution failures
- Processing timeout alerts
- Data validation errors
- Storage connectivity issues

## Usage Example

### File Upload Process

1. **Prepare Excel File**:
   ```
   OutletErpId | Latitude | Longitude | SubTerritoryId | TerritoryId | EmployeeId | TimePerVisit | VisitFrequency | EntityId
   OUT001      | 28.6139  | 77.2090   | ST001          | T001        | EMP001     | 30           | 2              | ENT001
   OUT002      | 28.6129  | 77.2080   | ST001          | T001        | EMP001     | 25           | 3              | ENT001
   ```

2. **Upload to Blob Storage**:
   ```bash
   # Upload to manual-uploads container
   az storage blob upload \
     --account-name <storage-account> \
     --container-name manual-uploads \
     --name "territory_data_2024.xlsx" \
     --file "./territory_data_2024.xlsx"
   ```

3. **Monitor Processing**:
   - Function automatically triggers on upload
   - Check Azure Function logs for progress
   - Monitor processing queue for completion status
   - Review generated files in `territory-optimization` container

### Expected Output
After successful processing:
```
territory-optimization/
└── 12345/  # table_id
    ├── territory_data_2024.xlsx  # Original file
    ├── output.xlsx               # Standardized copy
    └── ENT001/                   # Entity folder
        ├── territory_boundaries.json
        ├── subterritory_boundaries.json
        └── Territory/
            └── T001/
                └── territory_outlet.json
```

## Troubleshooting

### Common Issues

1. **Missing Required Columns**:
   - Error: "Missing required column: {column_name}"
   - Solution: Ensure Excel file contains all required columns

2. **Empty File Upload**:
   - Error: "Uploaded file is empty"
   - Solution: Verify file contains data rows

3. **Database Connection Failures**:
   - Error: "Failed to insert processing record into database"
   - Solution: Check database connectivity and permissions

4. **Storage Access Issues**:
   - Error: "Blob storage operation failed"
   - Solution: Verify storage account permissions and connection strings

### Debug Steps

1. **Check Function Logs**:
   ```bash
   func logs --function-name Manual_Upload_Pipeline
   ```

2. **Validate Input Data**:
   - Verify Excel file format and structure
   - Check for required columns and data types
   - Ensure coordinate values are valid

3. **Test Connectivity**:
   - Verify storage account access
   - Test database connection
   - Check queue service availability

4. **Monitor Resources**:
   - Check function execution time
   - Monitor memory usage
   - Verify storage quotas

## Support

For issues and questions:
1. Check function logs in Azure Portal
2. Review error messages in processing queue
3. Run local tests to isolate issues
4. Contact development team with error details and file samples