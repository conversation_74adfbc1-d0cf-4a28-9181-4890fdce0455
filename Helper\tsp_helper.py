import pandas as pd
import numpy as np
from haversine import haversine
from python_tsp.heuristics import solve_tsp_local_search
import os
import sys
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from global_constant import DIST_CONVT,OSRM_BASE_URL,SPEED
from Helper.osrm_helper import create_region_matrices,combine_distance_matrices
# Build augmented matrix with a dummy node at index 0

def build_augmented_matrix(coords):
    n = len(coords)
    D = np.zeros((n+1, n+1))
    # fill real→real
    for i in range(n):
        for j in range(n):
            D[i+1, j+1] = haversine(coords[i], coords[j]) * DIST_CONVT
    return D

def augmented_osrm_distance_matrix(current_day_data,OSRM_BASE_URL,country,DIST_CONVT,splitter_check = False):
    regional_dist_mat,all_regions,index_map = create_region_matrices(current_day_data,OSRM_BASE_URL,country)
    dist_matrix = combine_distance_matrices(current_day_data,regional_dist_mat,all_regions,index_map,DIST_CONVT)
    outlet_index_mapping = {}
    for indx_map_val in index_map.values():
        outlet_index_mapping.update(indx_map_val['outlet_idx_map'])
    outlet_index_mapping = {k: v + 1 for k, v in outlet_index_mapping.items()}
    if not splitter_check:
        dist_matrix = np.pad(dist_matrix, ((1, 0), (1, 0)), 'constant', constant_values=0)
    
    return dist_matrix,outlet_index_mapping
    

# Lin–Kernighan via python-tsp, then remove 0
def solve_open_tsp_lk(current_day_data,DIST_CONVT,country):
    try:
        indices = np.array(current_day_data.index.tolist())
        indices+=1
        outlet_index_mapping = dict(zip(current_day_data['OutletErpId'], indices))
        coords = list(zip(current_day_data['Latitude'], current_day_data['Longitude']))
        
        # build distance matrix
        # dist_matrix = build_augmented_matrix(coords)
        if country:
            dist_matrix,outlet_index_mapping = augmented_osrm_distance_matrix(current_day_data,OSRM_BASE_URL,country,DIST_CONVT)
        else:
            dist_matrix = build_augmented_matrix(coords)


        perm, dist= solve_tsp_local_search(dist_matrix)
        
        # rotate to dummy first
        i0 = perm.index(0)
        perm = perm[i0:] + perm[:i0] + [0]
        open_nodes = [v-1 for v in perm if v!=0]

        # prepare results
        results = []
        cumulative_distance = 0.0
        CumulativeTime = 0.0
        ConsecutiveOutletDistance = 0.0
        for i, node_idx in enumerate(open_nodes):
            current_node = current_day_data.iloc[node_idx]
            
            # Calculate CumulativeDistance if not first node
            if i > 0:
                prev_node = current_day_data.iloc[open_nodes[i-1]]
                # current_distance = haversine(
                #     (prev_node['Latitude'], prev_node['Longitude']),
                #     (current_node['Latitude'], current_node['Longitude'])
                # ) * DIST_CONVT

                current_distance = dist_matrix[outlet_index_mapping[prev_node['OutletErpId']]][outlet_index_mapping[current_node['OutletErpId']]]
                cumulative_distance += current_distance
                travel_time = cumulative_distance/SPEED
                CumulativeTime += (travel_time+(current_node['TimePerVisit']/60))
                ConsecutiveOutletDistance = current_distance
            
            results.append({
                'OutletErpId': str(current_node['OutletErpId']),
                'Latitude': float(current_node['Latitude']),
                'Longitude': float(current_node['Longitude']),
                'Sequence': i + 1,
                'CumulativeDistance': round(cumulative_distance, 6),
                'CumulativeTime': round(CumulativeTime, 6),
                'ConsecutiveOutletDistance': round(ConsecutiveOutletDistance, 6)
            })
        
        return results
        
    except Exception as e:
        print(f"Tsp generation failed")
        raise e


# Main function
def solve(raw_data:pd.DataFrame,internal_data:pd.DataFrame,country:str = None) -> dict:
    try:
        # Validate input data
        if country:
            required_columns = ['OutletErpId', 'Latitude', 'Longitude','TimePerVisit','Region']
            raw_data = pd.merge(raw_data,internal_data[['OutletErpId','Region']],how = 'left',left_on='OutletErpId',right_on='OutletErpId')
        else:
            required_columns = ['OutletErpId', 'Latitude', 'Longitude','TimePerVisit']
        if not all(col in raw_data.columns for col in required_columns):
            raise ValueError(f"Missing required columns: {required_columns}")
        
        raw_data.reset_index(drop=True, inplace=True)
        
        final_results = []

        # Apply LK
        results = solve_open_tsp_lk(raw_data,DIST_CONVT,country)

        # Append results
        final_results.extend(results)
            
        return_data = {"optimized_sequence" : final_results}
        return return_data
        
    except Exception as e:
        print(f"Optimized route sequence generation failed.")
        raise e
