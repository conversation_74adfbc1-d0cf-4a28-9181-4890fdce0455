import pandas as pd
import numpy as np
from haversine import haversine
from math import ceil
from concurrent.futures import ThreadPoolExecutor
import requests
import time
from global_constant import DIST_CONVT,BOUNDARY_FILES_PATH,OSRM_BASE_URL
from glob import glob
import os
import geopandas
from shapely.geometry import Polygon, MultiPolygon
from shapely.ops import unary_union
from concurrent.futures import ThreadPoolExecutor
import requests
import time
import logging
from copy import deepcopy
logging.basicConfig(level=logging.INFO,force=True)
import sentry_sdk

class InvalidLocationException(Exception):
    def __init__(self, message):
        super().__init__(message)

def distance_matrix(locations, dist_convert, multiplication=1):
    n = len(locations)
    distance_matrix = np.zeros((n, n))

    # Pre-calculate multiplication factor
    factor = dist_convert * multiplication
    
    for i in range(n):
        for j in range(i, n):  # Only compute upper triangle
            if i == j:
                distance = 0
            else:
                distance = haversine(locations[i], locations[j], unit="km") * factor
            distance_matrix[i, j] = distance
            distance_matrix[j, i] = distance  # Mirror to lower triangle
            
    return distance_matrix

def get_distance(loc_1, loc_2, dist, osrm_host):
    url = f"{osrm_host}/route/v1/driving/{loc_1[1]},{loc_1[0]};{loc_2[1]},{loc_2[0]}?overview=false"
    headers = {}
    headers["Authorization"] = f"Basic dXNlcjpFc2NhbGF0ZS1CYXJyYWNrLVBhcnRyaWRnZS1FeGNpdGFibGUtTGFua2luZXNz"
    try:
        response = requests.get(url, timeout=120,headers=headers,verify=False)
        response.raise_for_status()
        data = response.json()
        distances = data.get("routes", [])
        return distances[0]["distance"]
    except Exception as e:
        # print(f"Failed to get distance between {loc_1} and {loc_2}. Defaulting to haversine")
        return (haversine(loc_1, loc_2, unit="km") * DIST_CONVT) *1000

def chunking(i, j, locations, chunk_size, osrm_host, delay=1, authorization=None):
    src_chunk = locations[i:i + chunk_size].tolist()
    dst_chunk = locations[j:j + chunk_size].tolist()
    all_coords = src_chunk + dst_chunk
    coord_str = ";".join([f"{lon},{lat}" for lat, lon in all_coords])
    sources = ";".join(map(str, range(len(src_chunk))))
    destinations = ";".join(map(str, range(len(src_chunk), len(all_coords))))
    url = (
        f"{osrm_host}/table/v1/driving/{coord_str}"
        f"?sources={sources}&destinations={destinations}&annotations=distance"
    )
    headers = {}
    headers["Authorization"] = f"Basic dXNlcjpFc2NhbGF0ZS1CYXJyYWNrLVBhcnRyaWRnZS1FeGNpdGFibGUtTGFua2luZXNz"
    retries = 3
    for attempt in range(retries):
        try:
            response = requests.get(url, timeout=120,headers=headers,verify=False)
            response.raise_for_status()
            data = response.json()
            distances = data.get("distances", [])
            return (i, j, distances)
        except Exception as e:
            if attempt == retries - 1:
                try:
                    distances = distance_matrix(locations, DIST_CONVT)
                    print("defaulted to haversine")
                    return (i, j, distances)
                except Exception as e:
                    print(f"FAILURE Chunk (src: {i}-{i+len(src_chunk)}, dst: {j}-{j+len(dst_chunk)}): {e}")
                    sentry_sdk.capture_exception(e)
                    raise e
            else:
                time.sleep(delay)
                delay *= 2
                continue
 
def osrm_matrix(locations, chunk_size, max_workers ,osrm_host):

    n = len(locations)
    matrix = np.full((n, n), 0, dtype=np.float32)

    args_list = [(i, j, locations, chunk_size, osrm_host) 
                 for i in range(0, n, chunk_size)
                 for j in range(0, n, chunk_size)]
    # print("total chunks",len(args_list))
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        results = [executor.submit(chunking, *args) for args in args_list]
        results = [future.result() for future in results]
    
    for i, j, distances in results:
            for ii, row in enumerate(distances):
                for jj, dist in enumerate(row):
                    if i + ii < n and j + jj < n:
                        if dist is None:
                            dist = 0
                        elif dist < 0:
                            loc_1 = locations[i + ii]
                            loc_2 = locations[j + jj]
                            # dist = haversine(loc_1, loc_2, unit="km") * DIST_CONVT # change to routing api
                            dist = get_distance(loc_1, loc_2,dist, osrm_host)                                             
                        matrix[i + ii][j + jj] = dist/1000 
    print('MATRIX BUILT')
    return matrix

def build_distance_matrix(locations, idx_outlet_mapping, offloaded_distance_matrix, offloaded_outlet_index_mapping,salesman,multiplication=1):
        
        n = len(locations)
        combined_distance_matrix = np.zeros((n, n))
        
        for i in range(n):
            for j in range(i,n):
                if i != j:
                    row_idx = idx_outlet_mapping[i]
                    col_idx = idx_outlet_mapping[j]
                    # add a switch case where if outlet is not present in the offloaded_outlet_index_mapping. This case will come when adding outliers. Also  a good method incase if it fails, the route will still be generated
                    if row_idx not in offloaded_outlet_index_mapping[salesman].keys() or col_idx not in offloaded_outlet_index_mapping[salesman].keys():
                        distance = haversine(locations[i], locations[j], unit="km") * DIST_CONVT
                        distance = distance * multiplication
                        combined_distance_matrix[i][j] = distance
                        combined_distance_matrix[j][i] = distance
                        
                    else:
                        row_idx = offloaded_outlet_index_mapping[salesman][row_idx]
                        col_idx = offloaded_outlet_index_mapping[salesman][col_idx]
                        
                        combined_distance_matrix[i][j] = offloaded_distance_matrix[salesman][row_idx][col_idx]*multiplication
                        combined_distance_matrix[j][i] = offloaded_distance_matrix[salesman][col_idx][row_idx]*multiplication
                
        return combined_distance_matrix

def parse_poly_file(filepath):
    """
    Parses a .poly file treating each ring as a standalone outer polygon.
    Returns a MultiPolygon or Polygon depending on count.
    Skips invalid rings and logs warnings.
    """
    with open(filepath, 'r') as f:
        lines = [line.strip() for line in f if line.strip()]

    polygons = []
    current_coords = []
    i = 1  # Skip the first line (region name)

    while i < len(lines):
        line = lines[i]

        if line == 'END':
            if current_coords:
                try:
                    poly = Polygon(current_coords)
                    if poly.is_valid:
                        polygons.append(poly)
                    else:
                        logging.warning(f"Ignored invalid polygon in {filepath}")
                except Exception as e:
                    logging.error(f"Polygon creation error in {filepath}: {e}")
                current_coords = []
            
            # Check if the next line is also END (i.e., end of file)
            if i + 1 < len(lines) and lines[i + 1] == 'END':
                break

        elif line.isdigit():
            # Start of a new ring
            if current_coords:
                try:
                    poly = Polygon(current_coords)
                    if poly.is_valid:
                        polygons.append(poly)
                    else:
                        logging.warning(f"Ignored invalid polygon in {filepath}")
                except Exception as e:
                    logging.error(f"Polygon creation error in {filepath}: {e}")
                current_coords = []
        
        else:
            try:
                lon, lat = map(float, line.split())
                current_coords.append((lon, lat))
            except ValueError:
                logging.warning(f"Skipping malformed line: {line}")

        i += 1

    # Final wrap-up (if any ring left unprocessed)
    if current_coords:
        try:
            poly = Polygon(current_coords)
            if poly.is_valid:
                polygons.append(poly)
        except Exception as e:
            logging.error(f"Final polygon creation error in {filepath}: {e}")

    # Return geometry
    if not polygons:
        logging.warning(f"No valid polygons found in {filepath}")
        return None
    elif len(polygons) == 1:
        return polygons[0]
    else:
        return MultiPolygon(polygons)

def label_regions(filtered: pd.DataFrame,boundary_files):
        
        for poly_boundary in boundary_files:
            region_name = os.path.splitext(os.path.basename(poly_boundary))[0]
            all_polygons_union = []
            poly_geom = parse_poly_file(poly_boundary)
            
            if poly_geom:
                # If it is a multipolygon then append all geometries
                if isinstance(poly_geom, MultiPolygon):
                    all_polygons_union.extend(list(poly_geom.geoms))
                else:
                    all_polygons_union.append(poly_geom)
            else:
                logging.error(f"Could not parse polygon from {poly_boundary}")

            
            # Combine multipolygon if any
            region_boundary = None
            if all_polygons_union:
                region_boundary = unary_union(all_polygons_union)
            else:
                logging.error(f"Could not combine polygons from {poly_boundary}.No polygons were loaded from the specified .poly files.")
            
            # Create a geodataframe for points
            df = filtered[filtered['Region'].isna()]
            # check if all points are labelled
            if df.empty:
                return filtered
            
            df['idx'] = df.index
            gdf = geopandas.GeoDataFrame(data = df,geometry = geopandas.points_from_xy(df['Longitude'],df['Latitude']),crs = "EPSG:4326")
            
            # Create a geodataframe for region
            boundary_gdf = geopandas.GeoDataFrame(geometry=[region_boundary], crs="EPSG:4326")

            # Check points
            gdf['is_inside_region'] = gdf.geometry.apply(lambda p: boundary_gdf.contains(p).any() or boundary_gdf.touches(p).any())
            indices = gdf[gdf['is_inside_region']]['idx'].to_list()
            filtered.loc[indices,'Region'] = region_name
        
        return filtered

def create_region_matrices(filtered_1: pd.DataFrame,osrm_base_url:str,country:str):
        # calculate regional distance matrices
        regional_distance_matrices = []
        index_map ={}
        start_index = 0
        all_regions = filtered_1['Region'].unique()
        for region in all_regions:
            print(f"Building region matrix for {region}")
            region_data = filtered_1[filtered_1['Region'] == region]
            outlets = region_data['OutletErpId'].to_list()
            outlet_idx_map = {outlet_id: idx for idx, outlet_id in enumerate(outlets,start = start_index)}
            
            if region == country:
                osrm_host = f"{osrm_base_url}/{country}"
            else:
                osrm_host = f"{osrm_base_url}/{country}-{region}"
            locations = region_data[['Latitude', 'Longitude']].to_numpy()
            regional_distance_matrices.append(osrm_matrix(locations, chunk_size=1000, max_workers=4,osrm_host = osrm_host))
            
            indices = list(range(start_index, start_index + len(region_data)))
            index_map[region] = {
                'indices' : indices,
                'locations' : locations,
                'outlet_idx_map' : outlet_idx_map
            }
            start_index += len(region_data)
        
        return regional_distance_matrices,all_regions,index_map

def combine_distance_matrices(filtered_1,regional_distance_matrices,all_regions,index_map,dist_convert):
        # combine regional distance matrices
        combined_distance_matrix = np.zeros((len(filtered_1), len(filtered_1)))

        # fill intra region distances:
        for region, dist_matrix in zip(index_map.keys(),regional_distance_matrices):
            idxs = index_map[region]['indices']
            
            for i, row_idx in enumerate(idxs):
                for j, col_idx in enumerate(idxs):
                    combined_distance_matrix[row_idx][col_idx] = dist_matrix[i][j]

        # fill inter region distance
        for i in range(len(all_regions)):
            for j in range(i+1,len(all_regions)):
                region_i = all_regions[i]
                region_j = all_regions[j]
                
                idxs_i = index_map[region_i]['indices']
                idxs_j = index_map[region_j]['indices']
                locs_i = index_map[region_i]['locations']
                locs_j = index_map[region_j]['locations']

                for ii, loc_i in enumerate(locs_i):
                    for jj, loc_j in enumerate(locs_j):
                        dist = haversine(tuple(loc_i), tuple(loc_j), unit="km") * dist_convert

                        combined_distance_matrix[idxs_i[ii], idxs_j[jj]] = dist
                        combined_distance_matrix[idxs_j[jj], idxs_i[ii]] = dist

        return combined_distance_matrix

def extend_distance_matrix(matrix,index_outlet_mapping,home_location,filtered_1,dist_convert):
        n = matrix.shape[0]
        new_matrix = np.zeros((n+1,n+1))
        new_matrix[:n,:n] = matrix
        
        for i in range(n):
            outlet_id = index_outlet_mapping[i]
            location = filtered_1[filtered_1['OutletErpId'] == outlet_id][['Latitude', 'Longitude']].values[0]
            
            dist = haversine(tuple(home_location[0]), tuple(location), unit="km") * dist_convert
            new_matrix[i,n] = dist
            new_matrix[n,i] = dist
        
        return new_matrix

def is_outlier_OSRM(filtered: pd.DataFrame,dist_convert:float,country:str,osrm_base_url = OSRM_BASE_URL):

    # Labelling regions
    filtered['Region'] = None
    # Only Disable for Local Testing
    # cwd = os.path.dirname(__file__)
    # boundary_path = os.path.join(os.path.dirname(cwd),BOUNDARY_FILES_PATH,country)

    boundary_path = os.path.join(BOUNDARY_FILES_PATH,country)
    # logging.error(boundary_path)
    
    # All boundary files
    boundary_files = glob(os.path.join(boundary_path, "*.poly"))
    # logging.error(boundary_files)
    filtered = label_regions(filtered,boundary_files)

    # outside country outlier
    outside_country_indices = filtered[filtered['Region'].isna()].index
    
    # inside country data
    filtered_1 = filtered[filtered['Region'].notna()].reset_index(drop=True)

    # if no inside country data
    if filtered_1.empty:
        raise InvalidLocationException("No oulets' locations found in {country}")
    
    regional_distance_matrices,all_regions,index_map = create_region_matrices(filtered_1,osrm_base_url = osrm_base_url,country = country)
    
    combined_distance_matrix = combine_distance_matrices(filtered_1,regional_distance_matrices,all_regions,index_map,dist_convert)
    
    outlet_index_mapping = {}
    for indx_map_val in index_map.values():
        outlet_index_mapping.update(indx_map_val['outlet_idx_map'])
    index_outlet_mapping = {v: k for k, v in outlet_index_mapping.items()}

    
    # calculate outliers
    total_mean = combined_distance_matrix.mean() # Creating threshold mean + (std * 2)
    std_2 = combined_distance_matrix.std() * 2
    threshold = total_mean + std_2

    # Identifying outliers
    outlier_store = combined_distance_matrix > threshold
    
    # Finding indices of outliers
    outlier_counts = outlier_store.sum(axis=1)
    outlier_idxs = np.where(outlier_counts > 0.5 * (outlier_store.shape[0] - 1))[0]

    # getting ids for stores where outliers are greater than 50% of the store universe
    outlier_erp_id_list = list(map(lambda x: index_outlet_mapping[x], outlier_idxs))
    outisde_country_erp_ids = filtered.loc[outside_country_indices,'OutletErpId'].to_list()

    # label all outliers(outliers and inside country outliers)
    all_outliers = outlier_erp_id_list + outisde_country_erp_ids
    filtered['IsOutlier'] = filtered['OutletErpId'].apply(lambda x: True if x in all_outliers else False) # labelled_data
    
    return filtered
    



    
    

        


    