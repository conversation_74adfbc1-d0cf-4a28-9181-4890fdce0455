import json
from azure.storage.blob import BlobServiceClient, BlobClient, ContainerClient
from io import BytesIO
import pandas as pd
import time


def initialize_blob_service_client(connection_string: str) -> BlobServiceClient:
    """
    Initializes and returns a BlobServiceClient.

    :param connection_string: Azure Blob Storage connection string.
    :return: BlobServiceClient instance.
    """
    try:
        return BlobServiceClient.from_connection_string(connection_string)
    except Exception as e:
        print(f"Error initializing BlobServiceClient: {e}")
        raise


def get_container_client(
    blob_service_client: BlobServiceClient, container_name: str
) -> ContainerClient:
    """
    Gets or creates the container client.

    :param blob_service_client: BlobServiceClient instance.
    :param container_name: Name of the container.
    :return: ContainerClient object.
    """
    container_client = blob_service_client.get_container_client(container_name)
    if not container_client.exists():
        container_client.create_container()
    return container_client


def upload_stream_to_blob(
    container_client: ContainerClient,
    blob_name: str,
    data_stream: BytesIO,
    retry_count=3,
):
    """
    Uploads a data stream to an Azure Blob Storage.

    :param container_client: ContainerClient instance.
    :param blob_name: The name of the blob (file) in Azure Blob Storage.
    :param data_stream: Data stream to upload.
    :param retry_count: Number of times to retry the upload in case of timeout.
    """
    try:
        blob_client = container_client.get_blob_client(blob_name)
        for attempt in range(retry_count):
            try:
                blob_client.upload_blob(
                    data_stream, overwrite=True, timeout=600
                )  # Increased timeout
                print(
                    f"Data uploaded as '{blob_name}' to container '{container_client.container_name}'."
                )
                break
            except (TimeoutError) as e:
                print(f"Attempt {attempt + 1} failed with error: {e}. Retrying...")
                if attempt == retry_count - 1:
                    raise
                time.sleep(2**attempt)  # Exponential back-off retry mechanism
    except Exception as e:
        print(f"An error occurred while uploading the data: {e}")


def upload_excel_to_blob(
    blob_service_client: BlobServiceClient,
    container_name: str,
    blob_name: str,
    file_path: str,
):
    """
    Uploads an Excel file to Azure Blob Storage.

    :param blob_service_client: BlobServiceClient instance.
    :param container_name: Name of the container where the blob will be stored.
    :param blob_name: The name of the blob (file) in Azure Blob Storage.
    :param file_path: Path to the Excel file on the local filesystem.
    """
    container_client = get_container_client(blob_service_client, container_name)
    with open(file_path, "rb") as data:
        upload_stream_to_blob(container_client, blob_name, data)


def upload_dataframe_to_blob(
    blob_service_client: BlobServiceClient,
    container_name: str,
    blob_name: str,
    dataframe: pd.DataFrame,
):
    """
    Uploads a DataFrame as a CSV file to Azure Blob Storage.

    :param blob_service_client: BlobServiceClient instance.
    :param container_name: Name of the container.
    :param blob_name: Name of the blob (file).
    :param dataframe: pandas DataFrame to upload as CSV file.
    """
    csv_stream = BytesIO()
    dataframe.to_csv(csv_stream, index=False)
    csv_stream.seek(0)
    container_client = get_container_client(blob_service_client, container_name)
    upload_stream_to_blob(container_client, blob_name, csv_stream)


def download_blob_as_dataframe(
    blob_client: BlobClient, file_format: str = "csv"
) -> pd.DataFrame:
    """
    Downloads a blob as a pandas DataFrame.

    :param blob_client: BlobClient instance.
    :param file_format: Format of the file to be read ('csv' or 'excel').
    :return: pandas DataFrame containing the data from the blob.
    """
    download_stream = blob_client.download_blob()
    blob_data = download_stream.readall()
    data_stream = BytesIO(blob_data)

    if file_format == "csv":
        return pd.read_csv(data_stream)
    elif file_format == "excel":
        return pd.read_excel(data_stream, engine="openpyxl")


def read_blob_to_dataframe(
    blob_service_client: BlobServiceClient, container_name: str, blob_name: str
) -> pd.DataFrame:
    """
    Reads a CSV file from Azure Blob Storage and loads it into a pandas DataFrame.

    :param blob_service_client: BlobServiceClient instance.
    :param container_name: Name of the container where the blob is stored.
    :param blob_name: Name of the blob (file) to be read.
    :return: pandas DataFrame containing the CSV data from the blob.
    """
    blob_client = blob_service_client.get_blob_client(
        container=container_name, blob=blob_name
    )
    if blob_client.exists():
        return download_blob_as_dataframe(blob_client, file_format="csv")
    else:
        print("Blob does not exist.")
        return pd.DataFrame()


def read_blob_to_dataframe_excel(
    blob_service_client: BlobServiceClient, container_name: str, blob_name: str,sheet_name = None,result_updation = False
):
    """
    Reads an Excel file from Azure Blob Storage.

    :param blob_service_client: BlobServiceClient instance.
    :param container_name: Name of the container where the blob is stored.
    :param blob_name: Name of the blob (file) to be read.
    :return: Tuple of pandas DataFrames for the specified sheets.
    """
    blob_client = blob_service_client.get_blob_client(
        container=container_name, blob=blob_name
    )

    if blob_client.exists():
        print("Blob found, downloading data.")
        download_stream = blob_client.download_blob()
        blob_data = download_stream.readall()
        data_stream = BytesIO(blob_data)
        
        if not result_updation:
            data_stream.seek(0)  # Reset the stream position
            outlet_universe = pd.read_excel(
                data_stream, engine="openpyxl"
            )
            return outlet_universe
        else:
            data_stream.seek(0)
            outlet_universe = pd.read_excel(data_stream,sheet_name = sheet_name[0], engine="openpyxl")
            try:
                data_stream.seek(0)  # Reset the stream position 
                dq_data = pd.read_excel(data_stream, sheet_name= sheet_name[1], engine="openpyxl")
            except:
                print("Disqualified data doesn't exist")
                dq_data = pd.DataFrame()

        # visit_dump = pd.read_excel(data_stream, sheet_name="VD", engine="openpyxl")

            return outlet_universe, dq_data
    else:
        print("Blob does not exist.")
        return pd.DataFrame()  # return empty dataframes

def upload_to_blob(local_path, FileName, request_id, container_name, blob_service_client):
    # Get the container client
    container_client = blob_service_client.get_container_client(container_name)
    # Check if the container exists, if not, create it
    
    try:
        # Get the blob client
        blob_client = container_client.get_blob_client(f'{request_id}/output.xlsx')

        # Upload the file to the blob storage
        with open(local_path, "rb") as data:
            blob_client.upload_blob(data, overwrite=True)  # Set overwrite=True to replace existing blobs

        print(f"File {local_path} uploaded successfully to {container_name}.")

    except Exception as e:
        print(f"Error uploading file {local_path} to {container_name}: {e}")
        raise e

def upload_to_blob_generic(blob_service_client: BlobServiceClient,
    container_name: str,
    blob_name: str,
    local_path: str = None):

    """
        Upload a file or in-memory data to Azure Blob Storage.

        Args:
            blob_service_client (BlobServiceClient): Initialized client.
            container_name (str): Target container name.
            blob_name (str): Destination blob path (e.g. "12345/output.xlsx").
            local_path (str, optional): Path to local file to upload.
        """

    # Get the container client
    container_client = blob_service_client.get_container_client(container_name)
    # Check if the container exists, if not, create it
    
    try:
        # Get the blob client
        blob_client = container_client.get_blob_client(blob_name)
        # Upload the file to the blob storage
        with open(local_path, "rb") as data:
            blob_client.upload_blob(data, overwrite=True)  # Set overwrite=True to replace existing blobs

        print(f"File {local_path} uploaded successfully to {container_name}.")

    except Exception as e:
        print(f"Error uploading file {local_path} to {container_name}: {e}")
        raise e


def upload_json_to_blob(json_data, container_name, blob_name, blob_service_client):
    # Create a BlobClient to interact with the specific blob
    blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)
    
    # Convert the Python dictionary (or list) to a JSON string
    json_str = json.dumps(json_data)
    
    # Upload the JSON data as bytes
    blob_client.upload_blob(json_str, overwrite=True)  # `overwrite=True` ensures overwriting if the blob exists
    print(f"JSON file uploaded to blob: {container_name}/{blob_name}")

def read_json_from_blob(blob_service_client , container_name, blob_name):
    """
    Downloads and deserializes a JSON file from Azure Blob Storage.
    
    Args:
        blob_service_client: Azure blob service client
        container_name: Name of the container
        blob_name: Name/path of the blob to download
        
    Returns:
        dict: Deserialized JSON data, or empty dict if blob doesn't exist
    """
    # Get the container client
    container_client = blob_service_client.get_container_client(container_name)
    
    # Get the blob client
    blob_client = container_client.get_blob_client(blob_name)
    
    # Check if blob exists
    if blob_client.exists():
        # Download the blob
        download_stream = blob_client.download_blob()
        # Read and decode the JSON data
        json_data = download_stream.readall().decode('utf-8')
        # Parse JSON
        return json.loads(json_data)
    else:
        print(f"Blob {blob_name} does not exist.")
        raise Exception ("Blob does not exist.")