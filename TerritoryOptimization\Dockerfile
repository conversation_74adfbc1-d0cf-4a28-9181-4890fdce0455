# Use a slimmer azure functions as base image
FROM mcr.microsoft.com/azure-functions/python:4-python3.11-slim AS base

# setting environment variables for azure functions
ENV AzureWebJobsScriptRoot=/home/<USER>/wwwroot/TerritoryOptimization/ \
    AzureFunctionsJobHost__Logging__Console__IsEnabled=true

# set work directory
WORKDIR /home/<USER>/wwwroot/TerritoryOptimization/

# copy requirements file
COPY TerritoryOptimization/requirements.txt .

# install dependencies
RUN pip install --no-cache-dir -r requirements.txt

# COPY helper modules, utils and global constant
COPY TerritoryOptimization/ .
COPY Helper/ /home/<USER>/wwwroot/Helper/
COPY global_constant.py /home/<USER>/wwwroot/
COPY utils/ /home/<USER>/wwwroot/utils/

RUN apt-get update && apt-get install -y dos2unix

RUN dos2unix /home/<USER>/wwwroot/TerritoryOptimization/entrypoint.sh && \
    chmod +x /home/<USER>/wwwroot/TerritoryOptimization/entrypoint.sh


# Define entrypoint
ENTRYPOINT [ "./entrypoint.sh"]