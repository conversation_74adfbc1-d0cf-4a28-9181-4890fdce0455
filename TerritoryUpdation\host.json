{"version": "2.0", "concurrency": {"dynamicConcurrencyEnabled": true, "snapshotPersistenceEnabled": true}, "logging": {"logLevel": {"default": "Error", "Function": "Information"}}, "extensions": {"queues": {"maxPollingInterval": "00:00:02", "visibilityTimeout": "00:00:02", "batchSize": 1, "maxDequeueCount": 1, "newBatchThreshold": 2}}, "functionTimeout": "01:00:00", "extensionBundle": {"id": "Microsoft.Azure.Functions.ExtensionBundle", "version": "[4.*, 5.0.0)"}}