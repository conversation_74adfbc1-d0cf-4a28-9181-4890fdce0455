"""
Test script for the territory optimization data migration.
This script tests the migration functionality on a small subset of data.
"""

import os
import sys
import logging
from typing import List

# Add parent directory for imports
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)

from migrate_to_entity_structure import (
    discover_table_ids,
    check_if_already_migrated,
    migrate_table_folder,
    validate_migrated_data,
    MigrationStats
)
from Helper.blob_helper import initialize_blob_service_client
from global_constant import CONTAINER_NAME, STORAGE_CONN_STRING

# Configure logging for testing
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_migration.log'),
        logging.StreamHandler()
    ]
)

def test_discovery():
    """Test the table_id discovery functionality."""
    logging.info("Testing table_id discovery...")
    
    try:
        blob_service_client = initialize_blob_service_client(STORAGE_CONN_STRING)
        table_ids = discover_table_ids(blob_service_client, CONTAINER_NAME)
        
        logging.info(f"Discovery test successful. Found {len(table_ids)} table_ids")
        if table_ids:
            logging.info(f"Sample table_ids: {table_ids[:5]}")
        
        return table_ids
        
    except Exception as e:
        logging.error(f"Discovery test failed: {str(e)}")
        return []

def test_migration_check():
    """Test the migration status check functionality."""
    logging.info("Testing migration status check...")
    
    try:
        blob_service_client = initialize_blob_service_client(STORAGE_CONN_STRING)
        table_ids = discover_table_ids(blob_service_client, CONTAINER_NAME)
        
        if not table_ids:
            logging.warning("No table_ids found for migration check test")
            return
        
        # Test with first few table_ids
        test_table_ids = table_ids[:3]
        
        for table_id in test_table_ids:
            is_migrated = check_if_already_migrated(blob_service_client, CONTAINER_NAME, table_id)
            logging.info(f"Table {table_id}: {'Already migrated' if is_migrated else 'Not migrated'}")
        
        logging.info("Migration check test completed")
        
    except Exception as e:
        logging.error(f"Migration check test failed: {str(e)}")

def test_single_migration(table_id: str = None):
    """
    Test migration of a single table_id folder.
    
    Args:
        table_id: Specific table_id to test, or None to use first available
    """
    logging.info("Testing single table migration...")
    
    try:
        blob_service_client = initialize_blob_service_client(STORAGE_CONN_STRING)
        
        if table_id is None:
            table_ids = discover_table_ids(blob_service_client, CONTAINER_NAME)
            if not table_ids:
                logging.error("No table_ids found for single migration test")
                return False
            
            # Find a table that hasn't been migrated yet
            test_table_id = None
            for tid in table_ids:
                if not check_if_already_migrated(blob_service_client, CONTAINER_NAME, tid):
                    test_table_id = tid
                    break
            
            if test_table_id is None:
                logging.warning("All tables already migrated, testing with first table")
                test_table_id = table_ids[0]
        else:
            test_table_id = table_id
        
        logging.info(f"Testing migration for table_id: {test_table_id}")
        
        # Perform migration
        success = migrate_table_folder(blob_service_client, CONTAINER_NAME, test_table_id)
        
        if success:
            logging.info(f"Migration successful for table {test_table_id}")
            
            # Validate migration
            validation_success = validate_migrated_data(blob_service_client, CONTAINER_NAME, test_table_id)
            
            if validation_success:
                logging.info(f"Validation successful for table {test_table_id}")
                return True
            else:
                logging.error(f"Validation failed for table {test_table_id}")
                return False
        else:
            logging.error(f"Migration failed for table {test_table_id}")
            return False
            
    except Exception as e:
        logging.error(f"Single migration test failed: {str(e)}")
        return False

def test_batch_migration(max_tables: int = 3):
    """
    Test batch migration with a limited number of tables.
    
    Args:
        max_tables: Maximum number of tables to test
    """
    logging.info(f"Testing batch migration with max {max_tables} tables...")
    
    try:
        blob_service_client = initialize_blob_service_client(STORAGE_CONN_STRING)
        table_ids = discover_table_ids(blob_service_client, CONTAINER_NAME)
        
        if not table_ids:
            logging.error("No table_ids found for batch migration test")
            return False
        
        # Limit to max_tables for testing
        test_table_ids = table_ids[:max_tables]
        
        stats = MigrationStats()
        stats.total_folders = len(test_table_ids)
        
        logging.info(f"Testing batch migration with table_ids: {test_table_ids}")
        
        for i, table_id in enumerate(test_table_ids, 1):
            logging.info(f"Processing {i}/{len(test_table_ids)}: table_id {table_id}")
            
            try:
                # Check if already migrated
                if check_if_already_migrated(blob_service_client, CONTAINER_NAME, table_id):
                    stats.skipped_folders += 1
                    logging.info(f"Skipped table {table_id} (already migrated)")
                    continue
                
                # Migrate the folder
                if migrate_table_folder(blob_service_client, CONTAINER_NAME, table_id):
                    # Validate migration
                    if validate_migrated_data(blob_service_client, CONTAINER_NAME, table_id):
                        stats.successful_migrations += 1
                        stats.files_modified += 1
                        logging.info(f"Successfully migrated and validated table {table_id}")
                    else:
                        stats.failed_migrations += 1
                        stats.failed_table_ids.append(table_id)
                        logging.error(f"Migration validation failed for table {table_id}")
                else:
                    stats.failed_migrations += 1
                    stats.failed_table_ids.append(table_id)
                    logging.error(f"Migration failed for table {table_id}")
                    
            except Exception as e:
                stats.failed_migrations += 1
                stats.failed_table_ids.append(table_id)
                logging.error(f"Unexpected error migrating table {table_id}: {str(e)}")
        
        # Log test results
        stats.log_summary()
        
        return stats.failed_migrations == 0
        
    except Exception as e:
        logging.error(f"Batch migration test failed: {str(e)}")
        return False

def main():
    """Run all migration tests."""
    logging.info("Starting Migration Tests")
    logging.info("=" * 50)
    
    # Test 1: Discovery
    table_ids = test_discovery()
    if not table_ids:
        logging.error("Discovery test failed, cannot continue")
        return
    
    # Test 2: Migration status check
    test_migration_check()
    
    # Test 3: Single migration (optional - comment out if you don't want to modify data)
    # logging.info("\n" + "=" * 50)
    # single_success = test_single_migration()
    # logging.info(f"Single migration test: {'PASSED' if single_success else 'FAILED'}")
    
    # Test 4: Batch migration (optional - comment out if you don't want to modify data)
    # logging.info("\n" + "=" * 50)
    # batch_success = test_batch_migration(max_tables=2)
    # logging.info(f"Batch migration test: {'PASSED' if batch_success else 'FAILED'}")
    
    logging.info("\n" + "=" * 50)
    logging.info("Migration tests completed")
    logging.info("To run actual migration, uncomment the migration test sections above")
    logging.info("or run the main migration script: python migrate_to_entity_structure.py")

if __name__ == "__main__":
    main()
