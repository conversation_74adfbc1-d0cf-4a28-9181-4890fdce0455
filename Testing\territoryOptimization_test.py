import time
import os
import sys
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)
import warnings
from TerritoryOptimization.function_app import territory_generation
if __name__ == "__main__":
    warnings.filterwarnings("ignore")
    start_time = time.time()
    print("Start Time: " + time.ctime(start_time))
    territory_generation(10084,"309 single user 100 outlets.xlsx",10,5,5,20)
    end_time = time.time()
    print("End Time: "+time.ctime(start_time))
    total_time = end_time - start_time
    print(f"Total Time Duration: {total_time/60} min")
