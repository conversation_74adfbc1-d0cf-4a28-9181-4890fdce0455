import json
import logging
from azure.storage.queue import QueueServiceClient,BinaryBase64EncodePolicy, BinaryBase64DecodePolicy
import os
import sys
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)
import azure.functions as func
from Helper.queue_helper import throw_error
from Helper.blob_helper import initialize_blob_service_client, read_blob_to_dataframe_excel, upload_json_to_blob, upload_to_blob
from global_constant import CONTAINER_NAME, QUEUE_NAME_ERROR,STORAGE_CONN_STRING, DIST_CONVT,TABLE_NAME,OUTLET_COUNT_THRESHOLD
from utils import territory_opt_v6 as TO_v6
import sentry_sdk
from Helper.osrm_helper import InvalidLocationException

logging.basicConfig(level=logging.INFO,force=True)
logging.error("Initializing Sentry SDK...")
if os.environ.get("AppSettings__Deployment"):
    logging.error("Sentry SDK DSN Connection")
    sentry_sdk.init(
        dsn="http://<EMAIL>:8000/4",
        traces_sample_rate=1.0,
        environment=os.environ.get("AppSettings__Deployment")
    )
app = func.FunctionApp()

# Set up the Azure Function
@app.function_name(name="Territory_creation")
@app.queue_trigger(
    arg_name="msg",
    queue_name="territory-creation-queue",
    connection="StorageConnectionString"
)
async def queue_trigger(msg: func.QueueMessage) -> None:
    content = msg.get_body().decode("utf-8")
    content = json.loads(content)
    request_id = content['Id']
    file_name = content['FileName']
    n_clusters = content['NumberOfIterations']
    n_territories = content['NumberOfTerritories']
    accepted_deviation = content['FeasibleDeviation']
    ideal_outlet_cnt = content['IdealOutletCount']
    country = content['Country']
    territory_generation(request_id, file_name, n_clusters, n_territories, accepted_deviation, ideal_outlet_cnt,country)

def territory_generation(request_id, file_name, n_clusters, n_territories, accepted_deviation, ideal_outlet_cnt,country):
    blob_service_client = initialize_blob_service_client(STORAGE_CONN_STRING)
    queue_service_client = QueueServiceClient.from_connection_string(STORAGE_CONN_STRING)
    queue_client_error = queue_service_client.get_queue_client(QUEUE_NAME_ERROR)
    queue_service_client.message_encode_policy = BinaryBase64EncodePolicy()
    queue_service_client.message_decode_policy = BinaryBase64DecodePolicy()

    try:
        # download outlet universe from blob service client
        logging.info(f"Downloading outlet universe from blob service client for request_id: {request_id}")
        outlet_universe = read_blob_to_dataframe_excel(
            blob_service_client, CONTAINER_NAME, f'{request_id}/{file_name}')
        
        if outlet_universe.shape[0] > 0:
            cleaned_data_mfc,territory_boundaries,subterritory_boundaries,internal_output = TO_v6.territory_optimization(outlet_universe, DIST_CONVT, n_clusters, n_territories, accepted_deviation, ideal_outlet_cnt,country,OUTLET_COUNT_THRESHOLD)
            for territory_id in territory_boundaries:
                single_territory_df = cleaned_data_mfc[cleaned_data_mfc['TerritoryId'] == territory_id]
                single_territory_dict = single_territory_df.to_dict(orient='records')
                upload_json_to_blob(single_territory_dict,CONTAINER_NAME,F"{request_id}/Territory/{territory_id}/territory_outlet.json",blob_service_client)
            
            logging.info(f"Uploading territory boundaries for request_id: {request_id}")
            upload_json_to_blob(territory_boundaries, CONTAINER_NAME, f'{request_id}/territory_boundaries.json', blob_service_client)
            upload_json_to_blob(subterritory_boundaries, CONTAINER_NAME, f'{request_id}/subterritory_boundaries.json', blob_service_client)
            
            # uploading the output file
            upload_to_blob('output.xlsx',file_name, request_id, CONTAINER_NAME, blob_service_client)
            if not internal_output.empty:
                upload_to_blob('internal_output.xlsx',file_name, request_id, CONTAINER_NAME, blob_service_client)
                os.remove('internal_output.xlsx')
            # removing local file
            os.remove('output.xlsx')            
            
            logging.info(f"Uploading success message for request_id: {request_id}")
            success_message = {
                "Id": request_id,
                "TableName": TABLE_NAME,
                "Status": 20,
                "StatusRemark": f"Successfully Created Territories"
            }
            success_message_json = json.dumps(success_message)
            sucess_message_bytes = success_message_json.encode('utf-8')
            queue_client_error.send_message(queue_service_client.message_encode_policy.encode(content=sucess_message_bytes))
    except KeyError as e:
        missing_columns = list(e.args)
        throw_error(f"Missing column error: {missing_columns}",request_id,queue_client_error,queue_service_client)
        sentry_sdk.capture_exception(e)
        raise e
    except Exception as e:
        throw_error(f"Error During Territory Creation... ",request_id,queue_client_error,queue_service_client)
        sentry_sdk.capture_exception(e)
        raise e