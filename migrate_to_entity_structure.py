"""
Comprehensive Python script to migrate existing territory optimization data 
to the new entity-based folder structure.

This script handles backward compatibility while transforming data to match 
the current ManualUploadPipeline output format.
"""

import os
import sys
import json
import logging
import time
from typing import List, Dict, Tuple, Optional
import pandas as pd
from azure.storage.blob import BlobServiceClient
from io import BytesIO

# Add parent directory for imports
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)

from Helper.blob_helper import (
    initialize_blob_service_client, 
    read_blob_to_dataframe_excel,
    upload_to_blob_generic,
    upload_json_to_blob,
    read_json_from_blob,
    get_container_client
)
from global_constant import CONTAINER_NAME, STORAGE_CONN_STRING

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('migration.log'),
        logging.StreamHandler()
    ]
)

class MigrationStats:
    """Track migration statistics."""
    def __init__(self):
        self.total_folders = 0
        self.successful_migrations = 0
        self.failed_migrations = 0
        self.skipped_folders = 0
        self.files_modified = 0
        self.start_time = time.time()
        self.failed_table_ids = []
        
    def log_summary(self):
        """Log final migration summary."""
        duration = time.time() - self.start_time
        logging.info("=" * 60)
        logging.info("MIGRATION SUMMARY")
        logging.info("=" * 60)
        logging.info(f"Total folders processed: {self.total_folders}")
        logging.info(f"Successful migrations: {self.successful_migrations}")
        logging.info(f"Failed migrations: {self.failed_migrations}")
        logging.info(f"Skipped folders: {self.skipped_folders}")
        logging.info(f"Files modified: {self.files_modified}")
        logging.info(f"Total processing time: {duration:.2f} seconds")
        logging.info(f"Average time per folder: {duration/max(self.total_folders, 1):.2f} seconds")
        
        if self.failed_table_ids:
            logging.error(f"Failed table_ids: {self.failed_table_ids}")

def discover_table_ids(blob_service_client: BlobServiceClient, container_name: str) -> List[str]:
    """
    Discover all existing table_id folders in the container.
    
    Args:
        blob_service_client: Azure blob service client
        container_name: Name of the container
        
    Returns:
        List of table_id strings
    """
    logging.info("Discovering existing table_id folders...")
    
    container_client = get_container_client(blob_service_client, container_name)
    table_ids = set()
    
    # List all blobs and extract table_ids from paths
    blob_list = container_client.list_blobs()
    
    for blob in blob_list:
        blob_path = blob.name
        # Expected format: {table_id}/filename or {table_id}/subfolder/filename
        if '/' in blob_path:
            potential_table_id = blob_path.split('/')[0]
            # Check if it's a numeric table_id (assuming table_ids are numeric)
            if potential_table_id.isdigit():
                table_ids.add(potential_table_id)
    
    table_id_list = sorted(list(table_ids))
    logging.info(f"Found {len(table_id_list)} table_id folders: {table_id_list}")
    
    return table_id_list

def check_if_already_migrated(blob_service_client: BlobServiceClient, container_name: str, table_id: str) -> bool:
    """
    Check if a table_id folder has already been migrated to entity structure.
    
    Args:
        blob_service_client: Azure blob service client
        container_name: Name of the container
        table_id: Table ID to check
        
    Returns:
        True if already migrated, False otherwise
    """
    # Check for entity_list.json existence
    entity_list_blob_path = f"{table_id}/entity_list.json"
    blob_client = blob_service_client.get_blob_client(container=container_name, blob=entity_list_blob_path)
    
    if blob_client.exists():
        logging.info(f"Table {table_id} already migrated (entity_list.json exists)")
        return True
    
    # Check for Entity1 folder existence
    container_client = get_container_client(blob_service_client, container_name)
    blob_list = container_client.list_blobs(name_starts_with=f"{table_id}/Entity1/")
    
    for _ in blob_list:
        logging.info(f"Table {table_id} already migrated (Entity1 folder exists)")
        return True
    
    return False

def get_excel_files_in_folder(blob_service_client: BlobServiceClient, container_name: str, table_id: str) -> List[str]:
    """
    Get list of Excel files in a table_id folder.
    
    Args:
        blob_service_client: Azure blob service client
        container_name: Name of the container
        table_id: Table ID to check
        
    Returns:
        List of Excel file blob paths
    """
    container_client = get_container_client(blob_service_client, container_name)
    excel_files = []
    
    # List blobs in the table_id folder (not in subfolders)
    blob_list = container_client.list_blobs(name_starts_with=f"{table_id}/")
    
    for blob in blob_list:
        blob_path = blob.name
        # Only get files directly in table_id folder, not in subfolders
        path_parts = blob_path.split('/')
        if len(path_parts) == 2 and blob_path.endswith('.xlsx'):
            excel_files.append(blob_path)
    
    return excel_files

def add_entity_id_to_excel(blob_service_client: BlobServiceClient, container_name: str, blob_path: str) -> bool:
    """
    Add EntityId column to Excel file if not present.
    
    Args:
        blob_service_client: Azure blob service client
        container_name: Name of the container
        blob_path: Path to the Excel file blob
        
    Returns:
        True if successful, False otherwise
    """
    try:
        logging.info(f"Processing Excel file: {blob_path}")
        
        # Check if file is output.xlsx (has multiple sheets)
        is_output_file = blob_path.endswith('/output.xlsx')
        
        if is_output_file:
            # Handle multi-sheet Excel file
            try:
                territories_df, dq_df = read_blob_to_dataframe_excel(
                    blob_service_client, container_name, blob_path, 
                    sheet_name=['territories', 'DQ'], result_updation=True
                )
            except:
                # Fallback: try reading as single sheet
                territories_df = read_blob_to_dataframe_excel(
                    blob_service_client, container_name, blob_path
                )
                dq_df = pd.DataFrame()
        else:
            # Handle single-sheet Excel file
            territories_df = read_blob_to_dataframe_excel(
                blob_service_client, container_name, blob_path
            )
            dq_df = pd.DataFrame()
        
        # Check if EntityId column already exists
        if 'EntityId' in territories_df.columns:
            logging.info(f"EntityId column already exists in {blob_path}")
            return True
        
        # Add EntityId column after VisitFrequency
        if 'VisitFrequency' in territories_df.columns:
            # Get column order and insert EntityId after VisitFrequency
            cols = territories_df.columns.tolist()
            visit_freq_idx = cols.index('VisitFrequency')
            cols.insert(visit_freq_idx + 1, 'EntityId')
            
            # Add EntityId column with default value
            territories_df['EntityId'] = 'Entity1'
            
            # Reorder columns
            territories_df = territories_df[cols]
        else:
            # If VisitFrequency doesn't exist, add EntityId at the end
            territories_df['EntityId'] = 'Entity1'
        
        # Save modified Excel file
        local_excel_path = f"temp_modified_{blob_path.split('/')[-1]}"
        
        if is_output_file and not dq_df.empty:
            # Multi-sheet file
            with pd.ExcelWriter(local_excel_path, engine='openpyxl') as writer:
                territories_df.to_excel(writer, sheet_name='territories', index=False)
                dq_df.to_excel(writer, sheet_name='DQ', index=False)
        else:
            # Single sheet file
            territories_df.to_excel(local_excel_path, index=False, engine='openpyxl')
        
        # Upload modified file back to blob
        upload_to_blob_generic(blob_service_client, container_name, blob_path, local_excel_path)
        
        # Clean up local file
        if os.path.exists(local_excel_path):
            os.remove(local_excel_path)
        
        logging.info(f"Successfully added EntityId to {blob_path}")
        return True

    except Exception as e:
        logging.error(f"Failed to add EntityId to {blob_path}: {str(e)}")
        return False

def get_json_files_to_migrate(blob_service_client: BlobServiceClient, container_name: str, table_id: str) -> Dict[str, str]:
    """
    Get JSON files that need to be migrated to entity structure.

    Args:
        blob_service_client: Azure blob service client
        container_name: Name of the container
        table_id: Table ID to check

    Returns:
        Dictionary mapping file types to blob paths
    """
    container_client = get_container_client(blob_service_client, container_name)
    json_files = {
        'territory_boundaries': None,
        'subterritory_boundaries': None,
        'territory_outlets': []
    }

    # List all blobs in the table_id folder
    blob_list = container_client.list_blobs(name_starts_with=f"{table_id}/")

    for blob in blob_list:
        blob_path = blob.name

        if blob_path == f"{table_id}/territory_boundaries.json":
            json_files['territory_boundaries'] = blob_path
        elif blob_path == f"{table_id}/subterritory_boundaries.json":
            json_files['subterritory_boundaries'] = blob_path
        elif blob_path.startswith(f"{table_id}/Territory/") and blob_path.endswith('/territory_outlet.json'):
            json_files['territory_outlets'].append(blob_path)

    return json_files

def migrate_json_files(blob_service_client: BlobServiceClient, container_name: str, table_id: str) -> bool:
    """
    Move and update JSON files for entity structure.

    Args:
        blob_service_client: Azure blob service client
        container_name: Name of the container
        table_id: Table ID to migrate

    Returns:
        True if successful, False otherwise
    """
    try:
        logging.info(f"Migrating JSON files for table {table_id}")

        # Get JSON files to migrate
        json_files = get_json_files_to_migrate(blob_service_client, container_name, table_id)

        # Create entity_list.json
        entity_list = ["Entity1"]
        entity_list_path = f"{table_id}/entity_list.json"
        upload_json_to_blob(entity_list, container_name, entity_list_path, blob_service_client)
        logging.info(f"Created {entity_list_path}")

        # Move territory_boundaries.json
        if json_files['territory_boundaries']:
            try:
                territory_boundaries = read_json_from_blob(
                    blob_service_client, container_name, json_files['territory_boundaries']
                )
                new_path = f"{table_id}/Entity1/territory_boundaries.json"
                upload_json_to_blob(territory_boundaries, container_name, new_path, blob_service_client)
                logging.info(f"Moved territory_boundaries.json to {new_path}")
            except Exception as e:
                logging.warning(f"Could not migrate territory_boundaries.json: {str(e)}")

        # Move subterritory_boundaries.json
        if json_files['subterritory_boundaries']:
            try:
                subterritory_boundaries = read_json_from_blob(
                    blob_service_client, container_name, json_files['subterritory_boundaries']
                )
                new_path = f"{table_id}/Entity1/subterritory_boundaries.json"
                upload_json_to_blob(subterritory_boundaries, container_name, new_path, blob_service_client)
                logging.info(f"Moved subterritory_boundaries.json to {new_path}")
            except Exception as e:
                logging.warning(f"Could not migrate subterritory_boundaries.json: {str(e)}")

        # Move and update territory_outlet.json files
        for outlet_file_path in json_files['territory_outlets']:
            try:
                # Extract territory_id from path
                path_parts = outlet_file_path.split('/')
                territory_id = path_parts[2]  # table_id/Territory/{territory_id}/territory_outlet.json

                # Read existing outlet data
                outlet_data = read_json_from_blob(blob_service_client, container_name, outlet_file_path)

                # Add EntityId to each outlet record
                if isinstance(outlet_data, list):
                    for outlet in outlet_data:
                        if isinstance(outlet, dict) and 'EntityId' not in outlet:
                            outlet['EntityId'] = 'Entity1'

                # Upload to new location
                new_path = f"{table_id}/Entity1/Territory/{territory_id}/territory_outlet.json"
                upload_json_to_blob(outlet_data, container_name, new_path, blob_service_client)
                logging.info(f"Moved and updated {outlet_file_path} to {new_path}")

            except Exception as e:
                logging.warning(f"Could not migrate {outlet_file_path}: {str(e)}")

        return True

    except Exception as e:
        logging.error(f"Failed to migrate JSON files for table {table_id}: {str(e)}")
        return False

def migrate_table_folder(blob_service_client: BlobServiceClient, container_name: str, table_id: str) -> bool:
    """
    Migrate a single table_id folder to new entity-based structure.

    Args:
        blob_service_client: Azure blob service client
        container_name: Name of the container
        table_id: Table ID to migrate

    Returns:
        True if successful, False otherwise
    """
    try:
        logging.info(f"Starting migration for table {table_id}")

        # Check if already migrated
        if check_if_already_migrated(blob_service_client, container_name, table_id):
            logging.info(f"Table {table_id} already migrated, skipping")
            return True

        # Step 1: Add EntityId to Excel files
        excel_files = get_excel_files_in_folder(blob_service_client, container_name, table_id)
        logging.info(f"Found {len(excel_files)} Excel files to process")

        excel_success = True
        for excel_file in excel_files:
            if not add_entity_id_to_excel(blob_service_client, container_name, excel_file):
                excel_success = False

        if not excel_success:
            logging.error(f"Failed to update some Excel files for table {table_id}")
            return False

        # Step 2: Migrate JSON files and create entity structure
        if not migrate_json_files(blob_service_client, container_name, table_id):
            logging.error(f"Failed to migrate JSON files for table {table_id}")
            return False

        logging.info(f"Successfully migrated table {table_id}")
        return True

    except Exception as e:
        logging.error(f"Failed to migrate table {table_id}: {str(e)}")
        return False

def validate_migrated_data(blob_service_client: BlobServiceClient, container_name: str, table_id: str) -> bool:
    """
    Validate that migrated data is accessible and properly structured.

    Args:
        blob_service_client: Azure blob service client
        container_name: Name of the container
        table_id: Table ID to validate

    Returns:
        True if validation passes, False otherwise
    """
    try:
        logging.info(f"Validating migrated data for table {table_id}")

        # Check entity_list.json exists and is readable
        entity_list_path = f"{table_id}/entity_list.json"
        try:
            entity_list = read_json_from_blob(blob_service_client, container_name, entity_list_path)
            if not isinstance(entity_list, list) or "Entity1" not in entity_list:
                logging.error(f"Invalid entity_list.json for table {table_id}")
                return False
        except Exception as e:
            logging.error(f"Cannot read entity_list.json for table {table_id}: {str(e)}")
            return False

        # Check output.xlsx is readable with EntityId column
        output_path = f"{table_id}/output.xlsx"
        try:
            territories_df = read_blob_to_dataframe_excel(
                blob_service_client, container_name, output_path
            )
            if 'EntityId' not in territories_df.columns:
                logging.error(f"EntityId column missing in output.xlsx for table {table_id}")
                return False
        except Exception as e:
            logging.warning(f"Cannot validate output.xlsx for table {table_id}: {str(e)}")

        # Check Entity1 folder structure exists
        container_client = get_container_client(blob_service_client, container_name)
        entity_blobs = list(container_client.list_blobs(name_starts_with=f"{table_id}/Entity1/"))

        if not entity_blobs:
            logging.error(f"No Entity1 folder structure found for table {table_id}")
            return False

        logging.info(f"Validation passed for table {table_id}")
        return True

    except Exception as e:
        logging.error(f"Validation failed for table {table_id}: {str(e)}")
        return False

def run_migration_batch(blob_service_client: BlobServiceClient, container_name: str,
                       table_ids: List[str], start_idx: int = 0, batch_size: int = None) -> MigrationStats:
    """
    Run migration for a batch of table_ids.

    Args:
        blob_service_client: Azure blob service client
        container_name: Name of the container
        table_ids: List of table IDs to migrate
        start_idx: Starting index for batch processing
        batch_size: Number of table_ids to process (None for all)

    Returns:
        MigrationStats object with results
    """
    stats = MigrationStats()

    # Determine batch to process
    if batch_size is None:
        batch_table_ids = table_ids[start_idx:]
    else:
        batch_table_ids = table_ids[start_idx:start_idx + batch_size]

    stats.total_folders = len(batch_table_ids)

    logging.info(f"Starting migration batch: {len(batch_table_ids)} folders")
    logging.info(f"Table IDs to process: {batch_table_ids}")

    for i, table_id in enumerate(batch_table_ids, 1):
        logging.info(f"Processing {i}/{len(batch_table_ids)}: table_id {table_id}")

        try:
            # Check if already migrated
            if check_if_already_migrated(blob_service_client, container_name, table_id):
                stats.skipped_folders += 1
                logging.info(f"Skipped table {table_id} (already migrated)")
                continue

            # Migrate the folder
            if migrate_table_folder(blob_service_client, container_name, table_id):
                # Validate migration
                if validate_migrated_data(blob_service_client, container_name, table_id):
                    stats.successful_migrations += 1
                    stats.files_modified += 1  # At least one file was modified
                    logging.info(f"Successfully migrated and validated table {table_id}")
                else:
                    stats.failed_migrations += 1
                    stats.failed_table_ids.append(table_id)
                    logging.error(f"Migration validation failed for table {table_id}")
            else:
                stats.failed_migrations += 1
                stats.failed_table_ids.append(table_id)
                logging.error(f"Migration failed for table {table_id}")

        except Exception as e:
            stats.failed_migrations += 1
            stats.failed_table_ids.append(table_id)
            logging.error(f"Unexpected error migrating table {table_id}: {str(e)}")

    return stats

def main():
    """
    Main migration script entry point.
    """
    logging.info("Starting Territory Optimization Data Migration to Entity Structure")
    logging.info("=" * 70)

    try:
        # Initialize blob service client
        blob_service_client = initialize_blob_service_client(STORAGE_CONN_STRING)
        logging.info("Initialized Azure Blob Service Client")

        # Discover existing table_ids
        table_ids = discover_table_ids(blob_service_client, CONTAINER_NAME)

        if not table_ids:
            logging.warning("No table_id folders found to migrate")
            return

        # For testing, you can limit the number of folders to process
        # Uncomment the next line to test with first 3 folders only
        # table_ids = table_ids[:3]

        logging.info(f"Starting migration for {len(table_ids)} table_id folders")

        # Run migration
        stats = run_migration_batch(blob_service_client, CONTAINER_NAME, table_ids)

        # Log final summary
        stats.log_summary()

        # Exit with appropriate code
        if stats.failed_migrations > 0:
            logging.error("Migration completed with errors")
            sys.exit(1)
        else:
            logging.info("Migration completed successfully")
            sys.exit(0)

    except Exception as e:
        logging.error(f"Critical error in migration script: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
