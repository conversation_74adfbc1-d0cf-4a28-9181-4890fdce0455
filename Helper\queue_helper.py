import json
import logging
from azure.storage.queue import QueueServiceClient
from global_constant import TABLE_NAME


def push_blob_path_to_queue(queue_service_client, queue_name, blob_path, visibility_timeout):
    # Get the queue client
    queue_client = queue_service_client.get_queue_client(queue_name)
    msg = {
        "blob_path": blob_path,
        "validation_count": 0
    }
    msg_json = json.dumps(msg)
    msg_json_bytes = msg_json.encode('utf-8')
    # Push the blob path to the queue as a message
    queue_client.send_message(queue_service_client.message_encode_policy.encode(content=msg_json_bytes),visibility_timeout=visibility_timeout)

def throw_error(error_msg:str,id_value:int,queue_client_error:QueueServiceClient,queue_service_client:QueueServiceClient):
    error_messsage = {
            "Id": id_value,
            "Status": 30,
            "StatusRemark": f"{error_msg}",
            "TableName": TABLE_NAME
        }
    logging.error(f"Error during queue push: {error_msg}")
    error_message_json = json.dumps(error_messsage)
    error_message_bytes = error_message_json.encode('utf-8')
    queue_client_error.send_message(queue_service_client.message_encode_policy.encode(content=error_message_bytes))