# Use a smaller Azure Functions base image
FROM mcr.microsoft.com/azure-functions/python:4-python3.11-slim AS base

# Set Azure Functions environment variables 
ENV AzureWebJobsScriptRoot=/home/<USER>/wwwroot/ManualUploadPipeline/ \
    AzureFunctionsJobHost__Logging__Console__IsEnabled=true

WORKDIR /home/<USER>/wwwroot/ManualUploadPipeline/

# Copy only necessary files to reduce image size
COPY ManualUploadPipeline/requirements.txt /home/<USER>/wwwroot/ManualUploadPipeline/

# Install Python dependencies efficiently
RUN pip install --no-cache-dir -r /home/<USER>/wwwroot/ManualUploadPipeline/requirements.txt

# Copy the shared helper modules and global constants from the parent directory
COPY ManualUploadPipeline/ /home/<USER>/wwwroot/ManualUploadPipeline/
COPY Helper/ /home/<USER>/wwwroot/Helper/
COPY utils/ /home/<USER>/wwwroot/utils/
COPY global_constant.py /home/<USER>/wwwroot/global_constant.py
