import json
from azure.storage.queue import QueueServiceClient,BinaryBase64EncodePolicy, BinaryBase64DecodePolicy
import os
import sys
import pandas as pd
import logging
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)
import azure.functions as func
from Helper.queue_helper import throw_error
from Helper.blob_helper import initialize_blob_service_client, read_blob_to_dataframe_excel, read_json_from_blob, upload_to_blob, upload_json_to_blob
from global_constant import CONTAINER_NAME, QUEUE_NAME_ERROR,STORAGE_CONN_STRING,TABLE_NAME
from utils.helper import update_territory,boundary_updater,clean_data_updation
import sentry_sdk

logging.basicConfig(level=logging.INFO,force=True)
logging.error("Initializing Sentry SDK...")
if os.environ.get("AppSettings__Deployment"):
    logging.error("Sentry SDK DSN Connection")
    sentry_sdk.init(
        dsn="http://<EMAIL>:8000/4",
        traces_sample_rate=1.0,
        environment=os.environ.get("AppSettings__Deployment")
    )
app = func.FunctionApp()

# Set up the Azure Function
@app.function_name(name="Territory_updation")
@app.queue_trigger(
    arg_name="msg",
    queue_name="territory-updation-queue",
    connection="StorageConnectionString"
)
async def queue_trigger(msg: func.QueueMessage) -> None:
    content = msg.get_body().decode("utf-8")
    content = json.loads(content)
    request_id = content['Id']
    file_name = content['Filename']
    territory_updation(request_id, file_name)

def territory_updation(request_id, file_name):
    logging.info(f"Starting territory updation for request_id: {request_id}")
    blob_service_client = initialize_blob_service_client(STORAGE_CONN_STRING)
    queue_service_client = QueueServiceClient.from_connection_string(STORAGE_CONN_STRING)
    queue_client_error = queue_service_client.get_queue_client(QUEUE_NAME_ERROR)
    queue_service_client.message_encode_policy = BinaryBase64EncodePolicy()
    queue_service_client.message_decode_policy = BinaryBase64DecodePolicy()

    try:
        # download outlet universe from blob service client
        territory_df,dq_data = read_blob_to_dataframe_excel(
            blob_service_client, CONTAINER_NAME, f'{request_id}/output.xlsx',sheet_name = ['territories','DQ'],result_updation=True
        )
        
        territory_df = clean_data_updation(territory_df)
        
        territory_boundaries = read_json_from_blob(blob_service_client, CONTAINER_NAME, f'{request_id}/territory_boundaries.json')
        territory_changes = read_json_from_blob(blob_service_client, CONTAINER_NAME, f'{request_id}/{file_name}')

        if not territory_df.empty:
            updated_territory_df = update_territory(territory_df, territory_changes)
            updated_territory_boundaries = boundary_updater(updated_territory_df, territory_changes, territory_boundaries)
            
            updated_territory_df['TerritoryId'] = updated_territory_df['TerritoryId'].astype(int)
            # combine updated territory and disqualified data in one excel file named output.xlsx
            with pd.ExcelWriter('output.xlsx', engine='openpyxl') as writer:
                if not updated_territory_df.empty:
                    updated_territory_df.to_excel(writer, sheet_name='territories', index=False)
                if not dq_data.empty:
                    dq_data.to_excel(writer, sheet_name='DQ', index=False)
                                
            logging.info(f"Uploading output file for request_id: {request_id}")
            # upload the output file
            upload_to_blob('output.xlsx',file_name, request_id, CONTAINER_NAME, blob_service_client)
            # removing local file
            os.remove('output.xlsx')

            for territory_id in territory_changes:
                if len (territory_changes[territory_id]['Outlets']) > 0:
                    single_territory_df = updated_territory_df[updated_territory_df['TerritoryId'] == int(territory_id)]
                    single_territory_dict = single_territory_df.to_dict(orient='records')
                    upload_json_to_blob(single_territory_dict,CONTAINER_NAME,F"{request_id}/Territory/{territory_id}/territory_outlet.json",blob_service_client)

            # uploading updated_territories_boundaries json
            upload_json_to_blob(updated_territory_boundaries, CONTAINER_NAME, f'{request_id}/territory_boundaries.json', blob_service_client)
            
            logging.info(f"Uploading success message for request_id: {request_id}")
            success_message = {
                "Id": request_id,
                "Status": 20,
                "StatusRemark": f"Territory Updation Successful",
                "TableName": TABLE_NAME
            }
            success_message_json = json.dumps(success_message)
            sucess_message_bytes = success_message_json.encode('utf-8')
            queue_client_error.send_message(queue_service_client.message_encode_policy.encode(content=sucess_message_bytes))
    except KeyError as e:
        missing_columns = list(e.args)
        throw_error(f"Missing column error: {missing_columns}",request_id,queue_client_error,queue_service_client)
        sentry_sdk.capture_exception(e)
        raise e
    except Exception as e:
        throw_error(f"Error During Territory Updation... ",request_id,queue_client_error,queue_service_client)
        sentry_sdk.capture_exception(e)
        raise e