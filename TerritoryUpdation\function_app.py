import json
from azure.storage.queue import QueueServiceClient,BinaryBase64EncodePolicy, BinaryBase64DecodePolicy
import os
import sys
import pandas as pd
import logging
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)
import azure.functions as func
from Helper.queue_helper import throw_error
from Helper.blob_helper import initialize_blob_service_client, read_blob_to_dataframe_excel, read_json_from_blob, upload_to_blob, upload_json_to_blob
from global_constant import CONTAINER_NAME, QUEUE_NAME_ERROR,STORAGE_CONN_STRING,TABLE_NAME
from utils.helper import update_territory,boundary_updater,clean_data_updation
from Helper.tsp_helper import solve as sequencer
from Helper.osrm_helper import get_country_from_region
import sentry_sdk

logging.basicConfig(level=logging.INFO,force=True)
logging.error("Initializing Sentry SDK...")
if os.environ.get("AppSettings__Deployment"):
    logging.error("Sentry SDK DSN Connection")
    sentry_sdk.init(
        dsn="http://<EMAIL>:8000/4",
        traces_sample_rate=1.0,
        environment=os.environ.get("AppSettings__Deployment")
    )
app = func.FunctionApp()

# Set up the Azure Function
@app.function_name(name="Territory_updation")
@app.queue_trigger(
    arg_name="msg",
    queue_name="territory-updation-queue",
    connection="StorageConnectionString"
)
async def queue_trigger(msg: func.QueueMessage) -> None:
    content = msg.get_body().decode("utf-8")
    content = json.loads(content)
    request_id = content['Id']
    file_name = content['Filename']
    IsSequencerEnabled = content['IsSequencerEnabled']
    territory_updation(request_id, file_name,IsSequencerEnabled)

def territory_updation(request_id, file_name,IsSequencerEnabled):
    logging.info(f"Starting territory updation for request_id: {request_id}")
    blob_service_client = initialize_blob_service_client(STORAGE_CONN_STRING)
    queue_service_client = QueueServiceClient.from_connection_string(STORAGE_CONN_STRING)
    queue_client_error = queue_service_client.get_queue_client(QUEUE_NAME_ERROR)
    queue_service_client.message_encode_policy = BinaryBase64EncodePolicy()
    queue_service_client.message_decode_policy = BinaryBase64DecodePolicy()

    try:
        # download outlet universe from blob service client
        territory_df,dq_data = read_blob_to_dataframe_excel(
            blob_service_client, CONTAINER_NAME, f'{request_id}/output.xlsx',sheet_name = ['territories','DQ'],result_updation=True
        )
        internal_territory_df = read_blob_to_dataframe_excel(blob_service_client, CONTAINER_NAME, f'{request_id}/internal_output.xlsx')
        if not internal_territory_df.empty:
            country = get_country_from_region(internal_territory_df['Region'].iloc[0],parent_dir)
            internal_territory_df = clean_data_updation(internal_territory_df)
        else:
            country = None
        territory_df = clean_data_updation(territory_df)
        
        territory_boundaries = read_json_from_blob(blob_service_client, CONTAINER_NAME, f'{request_id}/territory_boundaries.json')
        
        subset_cols = ['OutletErpId','Latitude','Longitude','SubTerritoryId','TerritoryId','EmployeeId','TimePerVisit','VisitFrequency']

        if file_name is not None:
            territory_changes = read_json_from_blob(blob_service_client, CONTAINER_NAME, f'{request_id}/{file_name}')

            if not territory_df.empty:
                updated_territory_df = update_territory(territory_df, territory_changes)
                updated_territory_boundaries = boundary_updater(updated_territory_df, territory_changes, territory_boundaries)
                
                updated_territory_df['TerritoryId'] = updated_territory_df['TerritoryId'].astype(int)
                

                for territory_id in territory_changes:
                    if len (territory_changes[territory_id]['Outlets']) > 0:
                        single_territory_df = updated_territory_df[updated_territory_df['TerritoryId'] == int(territory_id)]
                        single_territory_df = single_territory_df[subset_cols]
                        single_territory_dict = single_territory_df.to_dict(orient='records')
                        upload_json_to_blob(single_territory_dict,CONTAINER_NAME,F"{request_id}/Territory/{territory_id}/territory_outlet.json",blob_service_client)

                # uploading updated_territories_boundaries json
                upload_json_to_blob(updated_territory_boundaries, CONTAINER_NAME, f'{request_id}/territory_boundaries.json', blob_service_client)
                
                if IsSequencerEnabled:
                    # run the sequencer on affected territories and save it in updated territory df
                    for territory_id in territory_changes:
                        if len (territory_changes[territory_id]['Outlets']) > 0:
                            mask = updated_territory_df['TerritoryId'] == int(territory_id)
                            single_territory_df = updated_territory_df[mask]
                            sequenced_data = sequencer(single_territory_df,internal_territory_df,country)
                            sequenced_data = pd.DataFrame(sequenced_data['optimized_sequence'])
                            single_territory_df.drop(columns = ['Sequence','CumulativeDistance','CumulativeTime','ConsecutiveOutletDistance'],axis = 1,inplace=True)
                            sequenced_data = sequenced_data[['OutletErpId','Sequence','CumulativeDistance','CumulativeTime','ConsecutiveOutletDistance']]

                            merged_data = pd.merge(single_territory_df,sequenced_data,how = 'left',left_on='OutletErpId',right_on='OutletErpId') 
                            updated_territory_df = pd.concat([updated_territory_df[~mask],merged_data]).reset_index(drop=True)
                        updated_territory_df.sort_values(by=['TerritoryId','Sequence'],ascending=[True,True],inplace=True)

                    
        else:
            territory_ids = territory_df['TerritoryId'].unique()
            sequenced_data_list = []
            for territory_id in territory_ids:
                single_territory_df = territory_df[territory_df['TerritoryId'] == territory_id]
                sequenced_data = sequencer(single_territory_df,internal_territory_df,country)
                sequenced_data = pd.DataFrame(sequenced_data['optimized_sequence'])
                sequenced_data = sequenced_data[['OutletErpId','Sequence','CumulativeDistance','CumulativeTime','ConsecutiveOutletDistance']]

                merged_data = pd.merge(single_territory_df,sequenced_data,how = 'left',left_on='OutletErpId',right_on='OutletErpId') 
                sequenced_data_list.append(merged_data)
            
            updated_territory_df = pd.concat(sequenced_data_list).reset_index(drop=True)
            updated_territory_df.sort_values(by=['TerritoryId','Sequence'],ascending=[True,True],inplace=True)

            
        
        # combine updated territory and disqualified data in one excel file named output.xlsx
        with pd.ExcelWriter('output.xlsx', engine='openpyxl') as writer:
            if not updated_territory_df.empty:
                updated_territory_df.to_excel(writer, sheet_name='territories', index=False)
            if not dq_data.empty:
                dq_data.to_excel(writer, sheet_name='DQ', index=False)
                            
        logging.info(f"Uploading output file for request_id: {request_id}")
        # upload the output file
        upload_to_blob('output.xlsx',file_name, request_id, CONTAINER_NAME, blob_service_client)
        # removing local file
        os.remove('output.xlsx')
        
        if IsSequencerEnabled:
            message = f"Territory Sequencing Successful"
        else:
            message = f"Territory Updation Successful"

        logging.info(f"Uploading success message for request_id: {request_id}")
        success_message = {
            "Id": request_id,
            "Status": 20,
            "StatusRemark": message,
            "TableName": TABLE_NAME
        }
        success_message_json = json.dumps(success_message)
        sucess_message_bytes = success_message_json.encode('utf-8')
        queue_client_error.send_message(queue_service_client.message_encode_policy.encode(content=sucess_message_bytes))
            
    except KeyError as e:
        missing_columns = list(e.args)
        throw_error(f"Missing column error: {missing_columns}",request_id,queue_client_error,queue_service_client)
        sentry_sdk.capture_exception(e)
        raise e
    except Exception as e:
        throw_error(f"Error During Territory Updation... ",request_id,queue_client_error,queue_service_client)
        sentry_sdk.capture_exception(e)
        raise e