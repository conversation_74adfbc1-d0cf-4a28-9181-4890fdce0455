# Use a smaller Azure Functions base image
FROM mcr.microsoft.com/azure-functions/python:4-python3.11-slim AS base

# Set Azure Functions environment variables
ENV AzureWebJobsScriptRoot=/home/<USER>/wwwroot/TerritoryUpdation/ \
    AzureFunctionsJobHost__Logging__Console__IsEnabled=true
    # ENV AzureWebJobsFaiDataLakeConnectionString="DefaultEndpointsProtocol=https;AccountName=faiadls;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
    # ENV AppSettings__Deployment="beta"

WORKDIR /home/<USER>/wwwroot/TerritoryUpdation/

# Copy only necessary files to reduce image size
COPY TerritoryUpdation/requirements.txt /home/<USER>/wwwroot/TerritoryUpdation/

# Install Python dependencies efficiently
RUN pip install --no-cache-dir -r /home/<USER>/wwwroot/TerritoryUpdation/requirements.txt

# Copy the shared helper modules and global constants from the parent directory
COPY TerritoryUpdation/ /home/<USER>/wwwroot/TerritoryUpdation/
COPY Helper/ /home/<USER>/wwwroot/Helper/
COPY utils/ /home/<USER>/wwwroot/utils/
COPY global_constant.py /home/<USER>/wwwroot/global_constant.py
