import time
import os
import sys
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)
from TerritoryUpdation.function_app import territory_updation
import warnings
if __name__ == "__main__":
    warnings.filterwarnings("ignore")
    start_time = time.time()
    print("Start Time: " + time.ctime(start_time))
    territory_updation(10084,"change-05/22/2025 11:03:02.json")
    end_time = time.time()
    print("End Time: "+time.ctime(start_time))
    total_time = end_time - start_time
    print(f"Total Time Duration: {total_time/60} min")