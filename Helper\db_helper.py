from typing import Any, Dict, List, Optional
import aioodbc
import pandas as pd
import configparser

async def get_config():
    config = configparser.ConfigParser()
    
    config.read("config_details/db_config.ini")
    server = config['Database']['server']
    database = config['Database']['database']
    username = config['Database']['username']
    pwd = config['Database']['password']
    encrypt = config['Database']['encrypt']
    trusted = config['Database']['trusted_connection']
    conn_timeout = config['Database']['connection_timeout']
    pool_size = config['Database']['max_pool_size']

    db_conn_str = f"DRIVER=ODBC Driver 18 for SQL Server;" \
              f"SERVER={server};" \
              f"DATABASE={database};" \
              f"UID={username};" \
              f"PWD={pwd};" \
              f"Trusted_Connection={trusted};" \
              f"ENCRYPT={encrypt};" \
              f"Connection Timeout={conn_timeout};" \
              f"Max Pool Size={pool_size};"

    return  db_conn_str

async def get_data_from_db(db_conn_str, query):
    # Establish the connection
    async with aioodbc.connect(dsn=db_conn_str) as conn:
        # Create a cursor
        async with conn.cursor() as cursor:
            # Execute the query
            await cursor.execute(query)
            # Fetch all rows from the executed query
            if cursor.description:
                rows = await cursor.fetchall()
                # Fetch column names from the cursor description
                columns = [desc[0] for desc in cursor.description]
                
                # Ensure the number of columns in rows matches the number of column names
                if len(rows) > 0 and len(columns) != len(rows[0]):
                    raise ValueError(f"Number of columns mismatch: expected {len(columns)} but got {len(rows[0])}")

                # Ensure that each row is a tuple
                rows = [tuple(row) for row in rows]

                # Construct a pandas DataFrame from the fetched data
                df = pd.DataFrame(rows, columns=columns)
                return df

async def insert_row(
    db_conn_str: str,
    table: str,
    data: Dict[str, Any],
    identity_column: str = "Id",
    autocommit: bool = True,
) -> Optional[int]:
    """
    Generic insert helper for SQL Server using aioodbc.
    Returns the value of `identity_column` from the inserted row.
    """
    if not data:
        raise ValueError("Insert data cannot be empty")

    # Build column & value lists
    columns = ", ".join(f"[{col}]" for col in data.keys())
    placeholders = ", ".join("?" for _ in data)
    values = tuple(data.values())

    # Use OUTPUT so the INSERT returns a result set
    sql = (
        f"INSERT INTO [{table}] ({columns}) "
        f"OUTPUT INSERTED.[{identity_column}] "
        f"VALUES ({placeholders});"
    )

    async with aioodbc.connect(dsn=db_conn_str, autocommit=autocommit) as conn:
        async with conn.cursor() as cur:
            await cur.execute(sql, values)
            row = await cur.fetchone()  # this WILL exist because OUTPUT returns rows
            if not autocommit:
                await conn.commit()
            return int(row[0]) if row and row[0] is not None else None

async def insert_data_with_id(table: str, data: Dict[str, Any]) -> int:
    """
    Simple wrapper to insert data and return the inserted row ID.

    Args:
        table: Table name
        data: Dictionary of column -> value

    Returns:
        int: The ID of the inserted row
    """
    db_conn_str = await get_config()
    return await insert_row(db_conn_str, table, data)