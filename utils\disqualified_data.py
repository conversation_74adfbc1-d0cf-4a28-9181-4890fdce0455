import pandas as pd

def save_disqualified_data(df: pd.DataFrame,rows_with_zero_lat_long: pd.DataFrame) -> pd.DataFrame:
    """
    Function to filter out the outliers from the dataframe and include outlets
    with Latitude or Longitude as 0. It saves the disqualified data to an Excel file
    with dynamic reasons for disqualification.
    
    Args:
    df (pd.DataFrame): The input DataFrame containing the data.
    output_file (str): The name of the output Excel file to write disqualified data.
    """
    
    # Create a new column for the reason for disqualification
   
    
    # Filter rows where 'isOutlier' is True or Latitude/Longitude is 0
    disqualified_df = df[(df['IsOutlier'] == True)]
    df_filtered = df[~df.index.isin(disqualified_df.index)]
    idx_used = disqualified_df['Idx'].tolist()

    disqualified_df = pd.concat([disqualified_df, rows_with_zero_lat_long], ignore_index=True)
    disqualified_df['ReasonForDisqualification'] = disqualified_df.apply(
        lambda row: 'OUTLIER' if row['IsOutlier'] else ('Invalid Latitude Longitude' if row['Latitude'] == 0 or row['Longitude'] == 0 else ''),
        axis=1
    )

    # Select only the relevant columns to include in the final output
    disqualified_df = disqualified_df[['EmployeeId','OutletErpId', 'Latitude', 'Longitude', 'ReasonForDisqualification']]
    
    return df_filtered,idx_used,disqualified_df
# save_disqualified_data(df, 'disqualified_data.xlsx')
