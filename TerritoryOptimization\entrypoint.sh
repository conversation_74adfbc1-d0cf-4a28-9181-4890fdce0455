#!/bin/bash

set -e  # Exit on error

# Install curl if not available
if ! command -v curl &> /dev/null; then
    echo "curl not found. Installing..."
    apt-get update && apt-get install -y curl
fi

# Create boundary_files directories
mkdir -p /home/<USER>/wwwroot/TerritoryOptimization/boundary_files/india
mkdir -p /home/<USER>/wwwroot/TerritoryOptimization/boundary_files/thailand
mkdir -p /home/<USER>/wwwroot/TerritoryOptimization/boundary_files/indonesia

# Download India zone poly files
curl -sSL https://download.geofabrik.de/asia/india/southern-zone.poly -o /home/<USER>/wwwroot/TerritoryOptimization/boundary_files/india/southern.poly
curl -sSL https://download.geofabrik.de/asia/india/eastern-zone.poly -o /home/<USER>/wwwroot/TerritoryOptimization/boundary_files/india/eastern.poly
curl -sSL https://download.geofabrik.de/asia/india/central-zone.poly -o /home/<USER>/wwwroot/TerritoryOptimization/boundary_files/india/central.poly
curl -sSL https://download.geofabrik.de/asia/india/northern-zone.poly -o /home/<USER>/wwwroot/TerritoryOptimization/boundary_files/india/northern.poly
curl -sSL https://download.geofabrik.de/asia/india/western-zone.poly -o /home/<USER>/wwwroot/TerritoryOptimization/boundary_files/india/western.poly
curl -sSL https://download.geofabrik.de/asia/india/north-eastern-zone.poly -o /home/<USER>/wwwroot/TerritoryOptimization/boundary_files/india/north-eastern.poly

# Download Indonesia zone poly files
curl -sSL https://download.geofabrik.de/asia/indonesia/java.poly -o /home/<USER>/wwwroot/TerritoryOptimization/boundary_files/indonesia/java.poly
curl -sSL https://download.geofabrik.de/asia/indonesia/kalimantan.poly -o /home/<USER>/wwwroot/TerritoryOptimization/boundary_files/indonesia/kalimantan.poly
curl -sSL https://download.geofabrik.de/asia/indonesia/maluku.poly -o /home/<USER>/wwwroot/TerritoryOptimization/boundary_files/indonesia/maluku.poly
curl -sSL https://download.geofabrik.de/asia/indonesia/nusa-tenggara.poly -o /home/<USER>/wwwroot/TerritoryOptimization/boundary_files/indonesia/nusa-tenggara.poly
curl -sSL https://download.geofabrik.de/asia/indonesia/papua.poly -o /home/<USER>/wwwroot/TerritoryOptimization/boundary_files/indonesia/papua.poly
curl -sSL https://download.geofabrik.de/asia/indonesia/sulawesi.poly -o /home/<USER>/wwwroot/TerritoryOptimization/boundary_files/indonesia/sulawesi.poly
curl -sSL https://download.geofabrik.de/asia/indonesia/sumatra.poly -o /home/<USER>/wwwroot/TerritoryOptimization/boundary_files/indonesia/sumatra.poly


# Download Thailand poly file
curl -sSL https://download.geofabrik.de/asia/thailand.poly -o /home/<USER>/wwwroot/TerritoryOptimization/boundary_files/thailand/thailand.poly

# Start Azure Functions host
exec /azure-functions-host/Microsoft.Azure.WebJobs.Script.WebHost