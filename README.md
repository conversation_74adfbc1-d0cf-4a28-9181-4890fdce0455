Read Input File (Single Excel File across 2 pages):

DJP - Sequencing: Sales Rep → Day → Outlet Mapping (New Route Plans)

DJP -  Incremental Changes: Sales Rep → Outlets (Unassigned Outlets)

Process Route Plans (DJP - Sequencing: ):

Optimize sequencing within given days using TSP + 2-opt.

Store the optimized route plans persistently.

Process Unassigned Outlets (DJP -  Incremental Changes):

Check if the outlet is already in the route plan:

Yes: Do nothing.

No: Assign it to the day of the nearest mapped outlet.

Re-optimize the sequence for that day and update the route plan.

NOTE :

If an outlet (O1) needs to be added to a route, and its nearest outlet (O2) is already visited twice in the plan (e.g., Wednesday and Friday), then O1 should be added to the earliest of those days—Wednesday in this case. The sequence follows a fixed weekly order from Saturday to Friday.

Sheet 1 will include a re-upload feature. After routes are uploaded/reuploaded, an icon will appear next to each row item. Clicking this icon will display outlets not found in our master list.

There will separate uploaders for both the sheets 

The following checks will be handled:

Day Sequence Validation: Ensures values in input match the accepted enum.

Lat/Lon Check: Flags entries with zero coordinates.

Duplicate Row Check: Identifies rows with identical values.
