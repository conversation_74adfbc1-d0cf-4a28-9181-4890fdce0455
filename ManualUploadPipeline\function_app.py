import datetime
import json
import logging
from typing import Optional
from azure.storage.queue import QueueServiceClient, BinaryBase64EncodePolicy, BinaryBase64DecodePolicy
from azure.storage.blob import BlobServiceClient
import os
import sys
import pandas as pd

parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)
import azure.functions as func
from Helper.queue_helper import throw_error
from Helper.blob_helper import initialize_blob_service_client, read_blob_to_dataframe_excel, upload_json_to_blob, upload_to_blob_generic
from Helper.db_helper import insert_data_with_id
from global_constant import CONTAINER_NAME, QUEUE_NAME_ERROR, STORAGE_CONN_STRING, TABLE_NAME
from utils.helper import boundary_finder
from utils.area_calc import generate_territory_area_and_overlap
import sentry_sdk

logging.basicConfig(level=logging.INFO, force=True)
logging.error("Initializing Sentry SDK...")
if os.environ.get("AppSettings__Deployment"):
    logging.error("Sentry SDK DSN Connection")
    sentry_sdk.init(
        dsn="http://<EMAIL>:8000/4",
        traces_sample_rate=1.0,
        environment=os.environ.get("AppSettings__Deployment")
    )

app = func.FunctionApp()

# # Set up the Azure Function for manual upload pipeline with blob trigger
@app.function_name(name="Manual_Upload_Pipeline")
@app.blob_trigger(
    arg_name="blob",
    path="workaraound-territory-optimization/{name}",
    connection="StorageConnectionString"
)
async def manual_upload_blob_trigger(blob: func.InputStream) -> None:
    """
    Azure Function triggered by blob upload for manual upload pipeline processing.

    The function processes files uploaded to the 'manual-uploads' container.
    File name format should be: {original_filename}

    Azure Functions will automatically handle retries on exceptions.
    """
    try:
        start_time = datetime.datetime.now()
        logging.info(f"Processing manual upload pipeline for file {blob.name}")

        request_id = await process_manual_upload(blob.name)

        logging.info(f"Successfully processed blob: {blob.name} and time taken is {datetime.datetime.now() - start_time} with request id {request_id}")

    except Exception as e:
        error_msg = f"Error in blob trigger for {blob.name}: {str(e)}"
        logging.error(error_msg)
        sentry_sdk.capture_exception(e)
        raise e  # Let Azure Functions handle retries

async def process_manual_upload(blob_name) -> Optional[int]:
    """
    Process the manual upload pipeline with the given parameters.

    Args:
        blob_name (str): Full blob path for blob trigger mode
    """

    blob_service_client = initialize_blob_service_client(STORAGE_CONN_STRING)
    queue_service_client = QueueServiceClient.from_connection_string(STORAGE_CONN_STRING)
    queue_client_error = queue_service_client.get_queue_client(QUEUE_NAME_ERROR)
    queue_service_client.message_encode_policy = BinaryBase64EncodePolicy()
    queue_service_client.message_decode_policy = BinaryBase64DecodePolicy()

    table_id = None

    try:
        # Extract filename from blob path
        original_filename = blob_name.split('/')[-1] if '/' in blob_name else blob_name

        # Download and validate the uploaded file
        logging.info(f"Downloading {blob_name} file from blob storage")
        uploaded_data = read_blob_to_dataframe_excel(
            blob_service_client, "workaround-territory-optimization", original_filename)

        if uploaded_data.shape[0] == 0:
            raise ValueError("Uploaded file is empty or could not be read")

        # Validate expected columns
        expected_columns = ['OutletErpId', 'Latitude', 'Longitude', 'SubTerritoryId',
                          'TerritoryId', 'EmployeeId', 'TimePerVisit', 'VisitFrequency', 'EntityId', 'Region']
        missing_columns = [col for col in expected_columns if col not in uploaded_data.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")

        logging.info(f"File validation successful. Found {uploaded_data.shape[0]} records")

        # STEP 1: Database Record Creation
        logging.info("Step 1: Creating database record in TerritoryOptimizationDetails")

        # Confirm with manas eacth details to put here
        db_record = {
            "UserId": 939847,  # fill dynamically
            "CompanyId": 67890,  # fill dynamically
            "UserRole": 0,  # map from PortalUserRole enum
            "InputFileName": original_filename,
            "CreatedAt": datetime.datetime.now(),
            "Status": 0,  # TerritoryOptimizationStatus enum -> int value
            "StatusRemark": "Manual upload processing started",
            "ExecutedAt": None,  # initially not executed
            "LastUpdatedAt": datetime.datetime.now(),
            "Email": "<EMAIL>",  # fill dynamically
            "InputConstraints": json.dumps({
                "NumberOfIterations": 200,
                "NumberOfTerritories": 12,
                "FeasibleDeviation": 12,
                "IdealOutletCount": 12,
                "IsSequencerEnabled": 0,
                "IsWorkAroundTO": 1
            }),  # optional JSON/text string
            "ActionTakenBy": 939847  # same as userId or another actor
        }

        # Insert record and get the table ID
        table_id = await insert_data_with_id(TABLE_NAME, db_record)
        if table_id is None:
            raise Exception("Failed to insert record into database")

        logging.info(f"Step 1 completed: Database record created with table_id: {table_id}")

        # STEP 2: File Storage Operations
        logging.info(f"Step 2: Storing files in territory-optimization/{table_id}/")

        # Save DataFrame as local Excel file first
        local_excel_path = "temp_output.xlsx"
        uploaded_data.to_excel(local_excel_path, index=False, engine='openpyxl')

        # Upload original file (same as output.xlsx for now)
        original_blob_path = f"{table_id}/{original_filename}"
        upload_to_blob_generic(blob_service_client, CONTAINER_NAME, original_blob_path, local_excel_path)

        # Upload as output.xlsx
        output_blob_path = f"{table_id}/output.xlsx"
        upload_to_blob_generic(blob_service_client, CONTAINER_NAME, output_blob_path, local_excel_path)

        # Clean up local file
        import os
        if os.path.exists(local_excel_path):
            os.remove(local_excel_path)

        logging.info(f"Step 2 completed: Files stored at {original_blob_path} and {output_blob_path}")

        # STEP 3: Entity-Based Data Organization and File Generation
        logging.info("Step 3: Creating entity-based data organization")

        grouping_column = 'EntityId'
        # Group data by the grouping column
        grouped_data = uploaded_data.groupby(grouping_column)

        # Create entity list and upload as JSON
        entity_list = list(grouped_data.groups.keys())
        logging.info(f"Found entities: {entity_list}")
        entity_list_path = f"{table_id}/entity_list.json"
        upload_json_to_blob(entity_list, CONTAINER_NAME, entity_list_path, blob_service_client)

        for entity_id, entity_data in grouped_data:
            logging.info(f"Processing entity: {entity_id} with {len(entity_data)} records")

            # Generate territory metrics for proper boundary calculation
            logging.info(f"Generating territory area and overlap metrics for entity: {entity_id}")
            territory_area, territory_overlap = generate_territory_area_and_overlap(entity_data)

            # Create dictionaries for area and overlap data
            territory_overlap_dict = {}
            if not territory_overlap.empty:
                territory_overlap_dict = pd.concat([
                    territory_overlap.groupby('Territory ID 1')['Overlapping Area (km²)'].sum(),
                    territory_overlap.groupby('Territory ID 2')['Overlapping Area (km²)'].sum()
                ]).groupby(level=0).sum().to_dict()

            territory_area_dict = {}
            if not territory_area.empty:
                territory_area_dict = territory_area.groupby('Territory ID')['Size/Area (km²)'].sum().to_dict()

            # Use boundary_finder to create proper territory boundaries with convex hull
            logging.info(f"Calculating territory boundaries using boundary_finder for entity: {entity_id}")
            territory_boundaries = boundary_finder(entity_data, territory_overlap_dict, territory_area_dict, sub_territory=False)

            # Use boundary_finder to create proper subterritory boundaries with convex hull
            logging.info(f"Calculating subterritory boundaries using boundary_finder for entity: {entity_id}")
            subterritory_boundaries = boundary_finder(entity_data, territory_overlap_dict, territory_area_dict, sub_territory=True)

            # Upload territory_boundaries.json
            territory_boundaries_path = f"{table_id}/{entity_id}/territory_boundaries.json"
            upload_json_to_blob(territory_boundaries, CONTAINER_NAME, territory_boundaries_path, blob_service_client)

            # Upload subterritory_boundaries.json
            subterritory_boundaries_path = f"{table_id}/{entity_id}/subterritory_boundaries.json"
            upload_json_to_blob(subterritory_boundaries, CONTAINER_NAME, subterritory_boundaries_path, blob_service_client)

            # Create Territory folder structure with territory_outlet.json files
            unique_territories = entity_data['TerritoryId'].unique()
            for territory_id in unique_territories:
                territory_outlets = entity_data[entity_data['TerritoryId'] == territory_id]
                territory_outlet_data = territory_outlets.to_dict(orient='records')

                territory_outlet_path = f"{table_id}/{entity_id}/Territory/{territory_id}/territory_outlet.json"
                upload_json_to_blob(territory_outlet_data, CONTAINER_NAME, territory_outlet_path, blob_service_client)

            logging.info(f"Completed processing entity: {entity_id}")

        logging.info("Step 3 completed: Entity-based data organization finished")

        # Send success message
        success_message = {
            "Id": table_id,
            "TableName": TABLE_NAME,
            "Status": 20,
            "StatusRemark": f"Manual upload pipeline completed successfully for {original_filename}"
        }
        success_message_json = json.dumps(success_message)
        success_message_bytes = success_message_json.encode('utf-8')
        queue_client_error.send_message(queue_service_client.message_encode_policy.encode(content=success_message_bytes))

        # after success full completion delete the file from manual-uploads container
        blob_service_client.delete_blob("workaraound-territory-optimization", blob_name)
        logging.info(f"Manual upload pipeline completed successfully for {original_filename}")

        return table_id

    except ValueError as e:
        error_msg = f"Validation error in manual upload pipeline: {str(e)}"
        logging.error(error_msg)
        if table_id:
            throw_error(error_msg, table_id, queue_client_error, queue_service_client)
        sentry_sdk.capture_exception(e)
        raise e
    except Exception as e:
        error_msg = f"Error in manual upload pipeline: {str(e)}"
        logging.error(error_msg)
        if table_id:
            throw_error(error_msg, table_id, queue_client_error, queue_service_client)
        sentry_sdk.capture_exception(e)
        raise e
